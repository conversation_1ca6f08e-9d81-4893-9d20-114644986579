--签到数据统计
SELECT
    create_date as name,
    count(distinct a.XGH) as yqdrs,
    COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END) as qdrs,
	round(COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END)/count(distinct a.XGH)*100,2) AS qdbl,
    count(distinct a.XGH)-COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END) wqdrs,
    round((count(distinct a.XGH)-COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END))/count(distinct a.XGH)*100,2) AS wddbl,
	COUNT(DISTINCT CASE WHEN r.SJZT = '异常' THEN r.XGH END) AS ycrs,
	round(COUNT(DISTINCT CASE WHEN r.SJZT = '异常' THEN r.XGH END)/count(distinct a.XGH)*100,2) AS ycbl,
	COUNT(DISTINCT CASE WHEN r.SJZT = '补签' THEN r.XGH END) AS bqrs,
    round(COUNT(DISTINCT CASE WHEN r.SJZT = '补签' THEN r.XGH END)/count(distinct a.XGH)*100,2) AS bqbl
FROM SYT_CHECKIN_RECORD r
    left join SYT_USER_INFO u on r.XGH = u.XGH
    left join SYT_CODE_DWB a on a.ID=u.XYID
    left join SYT_CODE_BJB c on c.ID=u.BJID
    left join SYT_SELECTOR_ACCOUNT a on r.ITEM_ID=a.ITEM_ID
WHERE r.item_id = '6f1be7b5edf304859f25166b5d54e896'
-- and r.create_date between '2024-04-01' and '2024-07-31'
-- and u.XYID='4f11f74bd08c6847954309f6852a06f4'
GROUP BY create_date;




WITH GroupedData AS (SELECT SFMC,
                            XYMC,
                            ZYMC,
                            NF, -- 添加 NF 字段以便排序
                            sum(decode(LQZY, NULL, 0, 1))                                                 AS lqs,
                            0                                                                             AS zjs,
                            0                                                                             AS zzys,
                            sum(decode(bdzt, '已报到', 1, 0))                                             AS bds,
                            ROUND((sum(decode(bdzt, '已报到', 1, 0)) / NULLIF(count(*), 0)) * 100, 2)     AS bdl,
                            sum(decode(XBMC, '男', 1, 0))                                                 AS nans,
                            ROUND((sum(decode(XBMC, '男', 1, 0)) / NULLIF(count(*), 0)) * 100, 2) || '%'  AS nanzb,
                            sum(decode(XBMC, '女', 1, 0))                                                 AS nvs,
                            ROUND((sum(decode(XBMC, '女', 1, 0)) / NULLIF(count(*), 0)) * 100, 2) || '%'  AS nvzb,
                            ROUND(MAX(to_number(TDCJ)), 2)                                                AS zgf,
                            ROUND(MIN(to_number(TDCJ)), 2)                                                AS zdf,
                            ROUND(AVG(to_number(TDCJ)), 2)                                                AS pjf,
                            MAX(TO_NUMBER(PRO_LINE))                                                      AS proLine,
                            ROUND(MAX(to_number(TDCJ)), 2) - MAX(TO_NUMBER(PRO_LINE))                     AS zgfxc,
                            ROUND(MIN(to_number(TDCJ)), 2) - MAX(TO_NUMBER(PRO_LINE))                     AS zdfxc,
                            ROUND(AVG(to_number(TDCJ)), 2) - MAX(TO_NUMBER(PRO_LINE))                     AS pjfxc,
                            COUNT(*)                                                                      AS group_count,
                            SUM(CASE WHEN zytj = '1' THEN 1 ELSE 0 END)                                   AS tj_count,
                            SUM(CASE WHEN YTZY IS NOT NULL THEN 1 ELSE 0 END)                             AS bk_count,
                            SUM(CASE WHEN kslqzy IS NOT NULL AND to_number(kslqzy) = 1 THEN 1 ELSE 0 END) AS zydh1_lq_count,
                            SUM(CASE WHEN kslqzy IS NOT NULL AND to_number(kslqzy) = 2 THEN 1 ELSE 0 END) AS zydh2_lq_count,
                            SUM(CASE WHEN kslqzy IS NOT NULL AND to_number(kslqzy) = 3 THEN 1 ELSE 0 END) AS zydh3_lq_count,
                            SUM(CASE WHEN kslqzy IS NOT NULL AND to_number(kslqzy) = 4 THEN 1 ELSE 0 END) AS zydh4_lq_count,
                            SUM(CASE WHEN kslqzy IS NOT NULL AND to_number(kslqzy) = 5 THEN 1 ELSE 0 END) AS zydh5_lq_count,
                            SUM(CASE WHEN kslqzy IS NOT NULL AND to_number(kslqzy) = 6 THEN 1 ELSE 0 END) AS zydh6_lq_count,
                            SUM(CASE WHEN ksytzy IS NOT NULL AND to_number(ksytzy) = 1 THEN 1 ELSE 0 END) AS zydh1_bk_count,
                            SUM(CASE WHEN ksytzy IS NOT NULL AND to_number(ksytzy) = 2 THEN 1 ELSE 0 END) AS zydh2_bk_count,
                            SUM(CASE WHEN ksytzy IS NOT NULL AND to_number(ksytzy) = 3 THEN 1 ELSE 0 END) AS zydh3_bk_count,
                            SUM(CASE WHEN ksytzy IS NOT NULL AND to_number(ksytzy) = 4 THEN 1 ELSE 0 END) AS zydh4_bk_count,
                            SUM(CASE WHEN ksytzy IS NOT NULL AND to_number(ksytzy) = 5 THEN 1 ELSE 0 END) AS zydh5_bk_count,
                            SUM(CASE WHEN ksytzy IS NOT NULL AND to_number(ksytzy) = 6 THEN 1 ELSE 0 END) AS zydh6_bk_count,
                            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY to_number(TDCJ))                  AS median,
                            PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY to_number(TDCJ))                 AS q1
                     FROM v_tdd
                     WHERE 1 = 1
                       AND NF IN ('2025')
                     GROUP BY SFMC, XYMC, ZYMC, NF), -- 在 GROUP BY 中也添加 NF
     RankedData AS (SELECT gd.*,
                           RANK() OVER (ORDER BY gd.zgf DESC)     AS zgf_rank,
                           RANK() OVER (ORDER BY gd.zdf DESC)     AS zdf_rank,
                           RANK() OVER (ORDER BY gd.pjf DESC)     AS pjf_rank,
                           RANK() OVER (ORDER BY gd.proLine DESC) AS proLine_rank,
                           COUNT(*) OVER ()                       AS total_count
                    FROM GroupedData gd),
     RankedWithPercentage AS (SELECT rd.*,
                                     ROUND((total_count - zgf_rank + 1) / NULLIF(total_count, 0) * 100, 2)     AS zgf_rank_percentage,
                                     ROUND((total_count - zdf_rank + 1) / NULLIF(total_count, 0) * 100, 2)     AS zdf_rank_percentage,
                                     ROUND((total_count - pjf_rank + 1) / NULLIF(total_count, 0) * 100, 2)     AS pjf_rank_percentage,
                                     ROUND((total_count - proLine_rank + 1) / NULLIF(total_count, 0) * 100,
                                           2)                                                                  AS proLine_rank_percentage,
                                     ROUND((tj_count / NULLIF(group_count, 0)) * 100, 2)                       AS tj_rate,
                                     ROUND((bk_count / NULLIF(group_count, 0)) * 100, 2)                       AS bk_rate,
                                     ROUND(zydh1_lq_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh1_lq_rate,
                                     ROUND(zydh2_lq_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh2_lq_rate,
                                     ROUND(zydh3_lq_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh3_lq_rate,
                                     ROUND(zydh4_lq_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh4_lq_rate,
                                     ROUND(zydh5_lq_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh5_lq_rate,
                                     ROUND(zydh6_lq_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh6_lq_rate,
                                     ROUND(zydh1_bk_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh1_bk_rate,
                                     ROUND(zydh2_bk_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh2_bk_rate,
                                     ROUND(zydh3_bk_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh3_bk_rate,
                                     ROUND(zydh4_bk_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh4_bk_rate,
                                     ROUND(zydh5_bk_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh5_bk_rate,
                                     ROUND(zydh6_bk_count / NULLIF(group_count, 0) * 100, 2)                   AS zydh6_bk_rate
                              FROM RankedData rd)
SELECT *
FROM RankedWithPercentage
ORDER BY NF DESC; -- 添加按 NF 倒序排序

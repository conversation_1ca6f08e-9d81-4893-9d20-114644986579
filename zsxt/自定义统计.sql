WITH GroupedData AS (
    SELECT
        nf,cc,ZSLX,sfmc,KLMC,PCMC,XYMC,ZYMC,KSLBMC,BYLBMC,WYYZMC,MZMC,ZZMMMC,TDDWDM,TDDWMC,
        sum(decode(LQZY, null, 0, 1)) AS lqs, 0 AS zjs, 0 AS zzys,
        sum(decode(bdzt, '已报到', 1, 0)) AS bds,
        ROUND((sum(decode(bdzt, '已报到', 1, 0)) / NULLIF(count(*), 0)) * 100, 2) AS bdl,
        sum(decode(XBMC, '男', 1, 0)) AS nans,
        ROUND((sum(decode(XBMC, '男', 1, 0)) / NULLIF(count(*), 0)) * 100, 2) || '%' AS nanzb,
        sum(decode(XBMC, '女', 1, 0)) AS nvs,
        ROUND((sum(decode(XBMC, '女', 1, 0)) / NULLIF(count(*), 0)) * 100, 2) || '%' AS nvzb,
        ROUND(MAX(to_number(TDCJ)), 2) AS zgf,
        ROUND(MIN(to_number(TDCJ)), 2) AS zdf,
        ROUND(AVG(to_number(TDCJ)), 2) AS pjf,
        MAX(TO_NUMBER(PRO_LINE)) AS proLine,
        ROUND(MAX(to_number(TDCJ)), 2) - MAX(TO_NUMBER(PRO_LINE)) AS zgfxc,
        ROUND(MIN(to_number(TDCJ)), 2) - MAX(TO_NUMBER(PRO_LINE)) AS zdfxc,
        ROUND(AVG(to_number(TDCJ)), 2) - MAX(TO_NUMBER(PRO_LINE)) AS pjfxc,
        COUNT(*) AS group_count,
        SUM(CASE WHEN zytj = '1' THEN 1 ELSE 0 END) AS tj_count,
        SUM(CASE WHEN YTZY IS NOT NULL THEN 1 ELSE 0 END) AS bk_count,
        -- 添加需要的字段
        SUM(CASE WHEN kslqzy is not null and to_number(kslqzy) = 1 THEN 1 ELSE 0 END) AS zydh1_lq_count,
        SUM(CASE WHEN kslqzy is not null and to_number(kslqzy) = 2 THEN 1 ELSE 0 END) AS zydh2_lq_count,
        SUM(CASE WHEN kslqzy is not null and to_number(kslqzy) = 3 THEN 1 ELSE 0 END) AS zydh3_lq_count,
        SUM(CASE WHEN kslqzy is not null and to_number(kslqzy) = 4 THEN 1 ELSE 0 END) AS zydh4_lq_count,
        SUM(CASE WHEN kslqzy is not null and to_number(kslqzy) = 5 THEN 1 ELSE 0 END) AS zydh5_lq_count,
        SUM(CASE WHEN kslqzy is not null and to_number(kslqzy) = 6 THEN 1 ELSE 0 END) AS zydh6_lq_count,
        SUM(CASE WHEN ksytzy is not null and to_number(ksytzy) = 1 THEN 1 ELSE 0 END) AS zydh1_bk_count,
        SUM(CASE WHEN ksytzy is not null and to_number(ksytzy) = 2 THEN 1 ELSE 0 END) AS zydh2_bk_count,
        SUM(CASE WHEN ksytzy is not null and to_number(ksytzy) = 3 THEN 1 ELSE 0 END) AS zydh3_bk_count,
        SUM(CASE WHEN ksytzy is not null and to_number(ksytzy) = 4 THEN 1 ELSE 0 END) AS zydh4_bk_count,
        SUM(CASE WHEN ksytzy is not null and to_number(ksytzy) = 5 THEN 1 ELSE 0 END) AS zydh5_bk_count,
        SUM(CASE WHEN ksytzy is not null and to_number(ksytzy) = 6 THEN 1 ELSE 0 END) AS zydh6_bk_count,
        -- 添加中位数和下四分位数统计
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY to_number(TDCJ)) AS median,
        PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY to_number(TDCJ)) AS q1
    FROM V_TDD
--     WHERE NF = '2024'
    GROUP BY nf,cc,ZSLX,sfmc,KLMC,PCMC,XYMC,ZYMC,KSLBMC,BYLBMC,WYYZMC,MZMC,ZZMMMC,TDDWDM,TDDWMC
),
RankedData AS (
    SELECT
        gd.*,
        RANK() OVER (ORDER BY gd.zgf DESC) AS zgf_rank,
        RANK() OVER (ORDER BY gd.zdf DESC) AS zdf_rank,
        RANK() OVER (ORDER BY gd.pjf DESC) AS pjf_rank,
        RANK() OVER (ORDER BY gd.proLine DESC) AS proLine_rank,
        COUNT(*) OVER () AS total_count
    FROM GroupedData gd
),
RankedWithPercentage AS (
    SELECT
        rd.*,
        ROUND((total_count - zgf_rank + 1) / NULLIF(total_count, 0) * 100, 2) AS zgf_rank_percentage,
        ROUND((total_count - zdf_rank + 1) / NULLIF(total_count, 0) * 100, 2) AS zdf_rank_percentage,
        ROUND((total_count - pjf_rank + 1) / NULLIF(total_count, 0) * 100, 2) AS pjf_rank_percentage,
        ROUND((total_count - proLine_rank + 1) / NULLIF(total_count, 0) * 100, 2) AS proLine_rank_percentage,
        ROUND((tj_count / NULLIF(group_count, 0)) * 100, 2) AS tj_rate,
        ROUND((bk_count / NULLIF(group_count, 0)) * 100, 2) AS bk_rate,
        -- 志愿录取数与录取率
        ROUND(zydh1_lq_count / NULLIF(group_count, 0) * 100, 2) AS zydh1_lq_rate,
        ROUND(zydh2_lq_count / NULLIF(group_count, 0) * 100, 2) AS zydh2_lq_rate,
        ROUND(zydh3_lq_count / NULLIF(group_count, 0) * 100, 2) AS zydh3_lq_rate,
        ROUND(zydh4_lq_count / NULLIF(group_count, 0) * 100, 2) AS zydh4_lq_rate,
        ROUND(zydh5_lq_count / NULLIF(group_count, 0) * 100, 2) AS zydh5_lq_rate,
        ROUND(zydh6_lq_count / NULLIF(group_count, 0) * 100, 2) AS zydh6_lq_rate,
        -- 志愿报考数与报考率
        ROUND(zydh1_bk_count / NULLIF(group_count, 0) * 100, 2) AS zydh1_bk_rate,
        ROUND(zydh2_bk_count / NULLIF(group_count, 0) * 100, 2) AS zydh2_bk_rate,
        ROUND(zydh3_bk_count / NULLIF(group_count, 0) * 100, 2) AS zydh3_bk_rate,
        ROUND(zydh4_bk_count / NULLIF(group_count, 0) * 100, 2) AS zydh4_bk_rate,
        ROUND(zydh5_bk_count / NULLIF(group_count, 0) * 100, 2) AS zydh5_bk_rate,
        ROUND(zydh6_bk_count / NULLIF(group_count, 0) * 100, 2) AS zydh6_bk_rate
    FROM RankedData rd
)
-- 层次_招生类型_省份_科类_批次_学院_专业名称_考生类别_毕业类别_外语语种_民族_政治面貌_年龄_投档单位代码_投档单位名称_
-- 录取数_征集数_转专业数_报到情况_性别情况_分数情况_省控线_线差情况_位次情况_位次占比情况_调剂情况_报考情况_一志愿录取情况_
-- 二志愿录取情况_三志愿录取情况_四志愿录取情况_五志愿录取情况_六志愿录取情况_一志愿报考情况_二志愿报考情况_三志愿报考情况_四志愿报考情况
-- _五志愿报考情况_六志愿报考情况_中位分_下四分位分_
SELECT
    nf,cc,ZSLX,sfmc,KLMC,PCMC,XYMC,ZYMC,KSLBMC,BYLBMC,WYYZMC,MZMC,ZZMMMC,TDDWDM,TDDWMC,
    lqs,zjs,zzys,bds,bdl,nans,nanzb,nvs,nvzb,
    zgf, zdf, pjf, proLine,zgfxc,zdfxc,pjfxc,group_count,
    tj_count, tj_rate,
    bk_count, bk_rate,
    zgf_rank, zgf_rank_percentage,
    zdf_rank, zdf_rank_percentage,
    pjf_rank, pjf_rank_percentage,
    proLine_rank, proLine_rank_percentage,
    zydh1_lq_count,zydh1_lq_rate,
    zydh2_lq_count,zydh2_lq_rate,
    zydh3_lq_count,zydh3_lq_rate,
    zydh4_lq_count,zydh4_lq_rate,
    zydh5_lq_count,zydh5_lq_rate,
    zydh6_lq_count,zydh6_lq_rate,
    zydh1_bk_count,zydh1_bk_rate,
    zydh2_bk_count,zydh2_bk_rate,
    zydh3_bk_count,zydh3_bk_rate,
    zydh4_bk_count,zydh4_bk_rate,
    zydh5_bk_count,zydh5_bk_rate,
    zydh6_bk_count,zydh6_bk_rate,
    -- 添加中位数和下四分位数到最终输出
    ROUND(median, 2) AS median,
    ROUND(q1, 2) AS q1
FROM RankedWithPercentage
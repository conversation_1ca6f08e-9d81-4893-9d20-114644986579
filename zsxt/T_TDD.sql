alter table T_<PERSON><PERSON> rename column FOCUSED_MU to FOCUSED_MUL
/

comment on column T_TDD.FOCUSED_MUL is 'FOCUSED_MUL'
/





alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX01 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX02 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX03 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX04 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX05 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX06 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX07 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TD<PERSON>
    add GKCJX08 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX09 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX12 VARCHAR2(255)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX18 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX19 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX20 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX21 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX23 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX50 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX51 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX52 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX53 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX54 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJX55 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJXZ1 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJXZ2 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJXZ3 VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJXZF VARCHAR2(255 char)
/

alter table SYT_NEW_ZSXT.T_TDD
    add GKCJXZM VARCHAR2(255 char)
/


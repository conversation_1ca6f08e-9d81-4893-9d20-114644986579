create table SYT_CODE
(
    ID            NUMBER(20) not null
        constraint SYT_CODE_PK
            primary key,
    DATASOURCE_ID NUMBER(20),
    SERVICE_NAME  NVARCHAR2(64),
    CODE_NAME     NVARCHAR2(64),
    TABLE_NAME    NVARCHAR2(64),
    TABLE_PREFIX  NVARCHAR2(64),
    PK_NAME       NVARCHAR2(32),
    PACKAGE_NAME  NVARCHAR2(500),
    BASE_MODE     NUMBER(11),
    WRAP_MODE     NUMBER(11),
    API_PATH      NCLOB,
    WEB_PATH      NCLOB,
    IS_DELETED    NUMBER(11)
)
/

comment on table SYT_CODE is '代码生成表'
/

comment on column SYT_CODE.ID is '主键'
/

comment on column SYT_CODE.DATASOURCE_ID is '数据源主键'
/

comment on column SYT_CODE.SERVICE_NAME is '服务名称'
/

comment on column SYT_CODE.CODE_NAME is '模块名称'
/

comment on column SYT_CODE.TABLE_NAME is '表名'
/

comment on column SYT_CODE.TABLE_PREFIX is '表前缀'
/

comment on column SYT_CODE.PK_NAME is '主键名'
/

comment on column SYT_CODE.PACKAGE_NAME is '后端包名'
/

comment on column SYT_CODE.BASE_MODE is '基础业务模式'
/

comment on column SYT_CODE.WRAP_MODE is '包装器模式'
/

comment on column SYT_CODE.API_PATH is '后端路径'
/

comment on column SYT_CODE.WEB_PATH is '前端路径'
/

comment on column SYT_CODE.IS_DELETED is '是否已删除'
/
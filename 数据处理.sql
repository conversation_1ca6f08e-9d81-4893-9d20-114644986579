-- 对 HUMANNAME 字段进行脱敏，只保留第一个字，其他部分替换为 *
UPDATE SYT_PERMISSION_ACCOUNT
SET HUMANNAME = SUBSTR(HUMANNAME, 1, 1) || RPAD('*', LENGTH(HUMANNAME) - 1, '*');

-- 对 TELMOBILE1 字段进行脱敏，将中间5位替换为 *
UPDATE SYT_PERMISSION_ACCOUNT
SET TELMOBILE1 = REGEXP_REPLACE(TELMOBILE1, '(^\d{3})\d{5}(\d{3})$', '\1*****\2');

-- 对 IDCODE 字段进行脱敏，将中间8位替换为 *
UPDATE SYT_PERMISSION_ACCOUNT
SET IDCODE = REGEXP_REPLACE(IDCODE, '(^\d{6})\d{8}(\d{4})$', '\1********\2');
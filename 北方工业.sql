-- 成绩预警
select * from (
select xh humancode,sum(jdcj*xf)/sum(xf) resval from st_xs_kccj
where xn='2021' and xq='A'
 group by xh
 ) where resval <60;
-- 安全预警
-- 1早上检查昨晚未归的,未归小时
select xh humancode,round(to_number((sysdate-to_date(chusj,'yyyy-MM-dd hh24:mi:ss')) * 24),1) resval from (
select t1.XH,max(t1.SKSJ) chusj,max(t2.SKSJ) jinsj from
(select xh,SKSJ from ST_MJSKJL a where CRLX='出') t1,
(select xh,SKSJ from ST_MJSKJL a where CRLX='进') t2
where t1.xh=t2.XH group by t1.XH
) where chusj>jinsj;
-- 2晚上检查未归宿
-- 消费预警
-- 1.贫困生天消费额超过50
select * from (
select xh,sum(XFJE) xfje from ST_XS_YKT_CENTER where
XFSJ between to_char(sysdate-1,'yyyy-MM-dd hh24:mi:ss') and to_char(sysdate,'yyyy-MM-dd hh24:mi:ss')
-- and xh in ('贫困生')
group by XH) where xfje>50;
-- 2.消费异常 日消费低于10
select * from (
select xh,sum(XFJE) xfje from ST_XS_YKT_CENTER where
XFSJ between to_char(sysdate-1,'yyyy-MM-dd hh24:mi:ss') and to_char(sysdate,'yyyy-MM-dd hh24:mi:ss')
group by XH) where xfje<10;
-- 自学态度预警
-- 1.周未进入图书馆





select xh humancode,ts resval from (
select xh,sysdate-(to_date(chusj,'yyyy-MM-dd hh24:mi:ss')) ts from (
select XH,chusj from (
select t1.XH,max(t1.SKSJ) chusj,max(t2.SKSJ) jinsj from
(select xh,SKSJ from ST_MJSKJL a where CRLX='出' and SKSJ between to_char(sysdate-1,'yyyy-MM-dd hh24:mi:ss') and to_char(sysdate,'yyyy-MM-dd hh24:mi:ss')) t1,
(select xh,SKSJ from ST_MJSKJL a where CRLX='进' and SKSJ between to_char(sysdate-1,'yyyy-MM-dd hh24:mi:ss') and to_char(sysdate,'yyyy-MM-dd hh24:mi:ss')) t2
where t1.xh=t2.XH group by t1.XH
) where chusj>jinsj
))
where ts>100;

update ST_MJSKJL set SKSJ='2022-08-23 23:09' where CRLX='出' and SKSJ like '2022-08-23%';
update ST_MJSKJL set SKSJ='2022-08-23 22:09' where CRLX='进' and SKSJ like '2022-08-23%';

select xh,SKSJ from ST_MJSKJL a where CRLX='出' and SKSJ between to_char(trunc(sysdate-1),'yyyy-MM-dd hh24:mi:ss') and to_char(sysdate,'yyyy-MM-dd hh24:mi:ss');
select SKSJ from ST_MJSKJL where CRLX='出' and SKSJ like '2022-08-2%';
select to_date(SKSJ,'yyyy-MM-dd hh24:mi:ss') from ST_MJSKJL;
select to_char(trunc(sysdate-1),'yyyy-MM-dd hh24:mi:ss') from DUAL;



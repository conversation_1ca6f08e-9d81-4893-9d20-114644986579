-- 创建视图传参对象
CREATE OR REPLACE PACKAGE syt_xxkjxg.pkg_view_param IS
   FUNCTION set_xmid(i_xmid VARCHAR2) RETURN VARCHAR2;
   FUNCTION get_xmid RETURN VARCHAR2;
   FUNCTION set_sqsj(i_sqsj VARCHAR2) RETURN VARCHAR2;
   FUNCTION get_sqsj RETURN VARCHAR2;
END pkg_view_param;
CREATE OR REPLACE PACKAGE BODY syt_xxkjxg.pkg_view_param IS
   -- 全局变量
   g_xmid       VARCHAR2(100);
   g_sqsj       VARCHAR2(100);

   FUNCTION set_xmid(i_xmid VARCHAR2) RETURN VARCHAR2 IS
   BEGIN
      g_xmid := i_xmid;
      RETURN i_xmid;
   END;

   FUNCTION get_xmid RETURN VARCHAR2 IS
   BEGIN
      RETURN g_xmid;
   END get_xmid;
   FUNCTION set_sqsj(i_sqsj VARCHAR2) RETURN VARCHAR2 IS
   BEGIN
      g_sqsj := i_sqsj;
      RETURN i_sqsj;
   END;

   FUNCTION get_sqsj RETURN VARCHAR2 IS
   BEGIN
      RETURN g_sqsj;
   END get_sqsj;
END pkg_view_param;
--学生总数视图
create or replace view v_sjsb_xjb as
select * from st_xs_xjb where SFBYS='否' and
xh in (select xh from ST_ZZGL_TSMD where XMID='ff8080817f779108017f9a9e1ae64b8b' and PDNF='202002') ;
--按学生分组获取每日健康上报最新日期的数据
create or replace view V_ZZGL_XSSQB_LATEST as
select * from (
select ROW_NUMBER() over (partition by s.XJB_ID order by SQSJ desc) rn,
s.*
from st_zzgl_xssqb s
where s.xmqkb_id = PKG_VIEW_PARAM.GET_XMID() and s.SQSJ<=PKG_VIEW_PARAM.GET_SQSJ()
) where rn=1;

--按学生分组获取每日健康上报最近日期的两条数据,用于区分返京和离京的学生
create or replace view V_ZZGL_XSSQB_LATEST_TWO as
select
    *
from (
select ROW_NUMBER() over (partition by s.XJB_ID order by SQSJ desc) rn,
s.*
from st_zzgl_xssqb s
where s.xmqkb_id = PKG_VIEW_PARAM.GET_XMID() and s.SQSJ<=PKG_VIEW_PARAM.GET_SQSJ()
) where rn in (1,2);

-- -------------------------------------------------------------------------------------------------------------------------------------------------
-- 项目sql备份
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('京外研究生名单', '京外研究生', 'select%20sqsj%2Cxjb_id%2Cxm%2Cxb%2Cdwmc%2Cbjmc%2Cnjmc%2Cpyccmc%2Cfdyxm%2Clocation_address%2Cc2%2Cc3%2Cc4%2Cc5%2Cc10%2Cc15%2Cc1%2Cc6%2Cc12%2Cc7%2Cc13%2Cc8%2Cc11%2Cc16%2Cc17%2Cc9%2Cc14%2Cc18%2Cc19%2Cc20%2Cc21%2Cc22%2Cc23%20from%20(select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.location_address%2Ct.c2%2Ct.c3%2Ct.c4%2Ct.c5%2Ct.c10%2Ct.c15%2Ct.c1%2Ct.c6%2Ct.c12%2Ct.c7%2Ct.c13%2Ct.c8%2Ct.c11%2Ct.c16%2Ct.c17%2Ct.c9%2Ct.c14%2Ct.c18%2Ct.c19%2Ct.c20%2Ct.c21%2Ct.c22%2Ct.c23%2C%20ROW_NUMBER()%20OVER(PARTITION%20BY%20t.xjb_id%20ORDER%20BY%20t.sqsj)%20AS%20px%20%0A%20%20%20%20%20%20%20from%20V_ZZGL_XSSQB_LATEST%20t%20left%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0A%20%20%20%20%20%20%20where%20PKG_VIEW_PARAM.SET_XMID(''%23xmid'')%3D''%23xmid''%20and%20PKG_VIEW_PARAM.SET_SQSJ(''%23sqsj'')%3D''%23sqsj''%0A%20%20%20%20%20%20%20and%20location_address%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25''%20%20and%20%20x.pyccmc%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25'')%0Awhere%20px%3D1%20%20%23roleWhere%20order%20by%20sqsj%2Cdwmc%2Cbjmc%2Cnjmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('当日返校研究生', '今日返校研究生', 'select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.location_address%20from%20st_zzgl_xssqb%20t
left%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh
where%20t.xmqkb_id%3D%27%23xmid%27%20and%20t.sqsj%3D%27%23sqsj%27
and%20t.c2%3D%27%E5%9C%A8%E6%A0%A1%27
and%20t.xjb_id%20in(select%20xjb_id%20from%20st_zzgl_xssqb%20where%20xmqkb_id%3D%27%23xmid%27%20and%20sqsj%3Dto_char(to_date(sqsj%2C%27YYYY-MM-DD%27)-1%2C%27YYYY-MM-DD%27)%20and%20c2%3D%27%E4%B8%8D%E5%9C%A8%E6%A0%A1%27%20%20and%20x.pyccmc%20%20like%20%27%25%E7%A0%94%E7%A9%B6%E7%94%9F%25%27)
%23roleWhere%20%20order%20by%20%20dwmc%2Cbjmc%2Cnjmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('京外本科生名单', '培养层次统计', 'select%20PYCCMC%2Ccount(*)%20rs%20from(%0Aselect%20sqsj%2Cxjb_id%2Cxm%2Cxb%2Cdwmc%2Cbjmc%2Cnjmc%2Cpyccmc%2Cfdyxm%2Clocation_address%20%20from%20(%0A%20%20%20%20%20%20%20select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.location_address%2C%0A%20%20%20%20%20%20%20ROW_NUMBER()%20OVER(PARTITION%20BY%20t.xjb_id%20ORDER%20BY%20t.sqsj)%20AS%20px%20%0A%20%20%20%20%20%20%20from%20V_ZZGL_XSSQB_LATEST%20t%20left%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0A%20%20%20%20%20%20%20where%20PKG_VIEW_PARAM.SET_XMID(''%23xmid'')%3D''%23xmid''%20and%20PKG_VIEW_PARAM.SET_SQSJ(''%23sqsj'')%3D''%23sqsj''%0A%20%20%20%20%20%20%20and%20location_address%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25''%20%20and%20%20x.pyccmc%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25'')%0Awhere%20px%3D1%20%20%23roleWhere%20order%20by%20sqsj%2Cdwmc%2Cbjmc%2Cnjmc%0A)%20group%20by%20PYCCMC');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('在校研究生', '在校研究生', 'SELECT%20sqsj%2Cxjb_id%2Cxm%2Cxb%2Cdwmc%2Cbjmc%2Cnjmc%2Cpyccmc%2Cfdyxm%2Cc2%2Clocation_address%2Cc3%2Cc4%2Cc5%2Cc10%2Cc15%2Cc1%2Cc6%2Cc12%2Cc7%2Cc13%2Cc8%2Cc11%2Cc16%2Cc17%2Cc9%2Cc14%2Cc18%2Cc19%2Cc20%2Cc21%2Cc22%2Cc23%20FROM(%0ASELECT%20%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.c2%2Ct.location_address%2Ct.c3%2Ct.c4%2Ct.c5%2Ct.c10%2Ct.c15%2Ct.c1%2Ct.c6%2Ct.c12%2Ct.c7%2Ct.c13%2Ct.c8%2Ct.c11%2Ct.c16%2Ct.c17%2Ct.c9%2Ct.c14%2Ct.c18%2Ct.c19%2Ct.c20%2Ct.c21%2Ct.c22%2Ct.c23%2C%0A%20ROW_NUMBER()%20OVER(PARTITION%20BY%20t.xjb_id%20ORDER%20BY%20t.sqsj)%20AS%20px%20from%20st_zzgl_xssqb%20t%20%0Aleft%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0Awhere%20t.xmqkb_id%3D''%23xmid''%20and%20t.c2%3D''%E5%9C%A8%E6%A0%A1''%20%20and%20t.sqsj%3C%3D''%23sqsj''%20%20%20and%20x.pyccmc%20%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25''%0A)%0AWHERE%20px%20%3D1%20%20%23roleWhere%20order%20by%20%20dwmc%2Cbjmc%2Cnjmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('当日返京本科生', '今日返京本科生', 'select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.location_address%2Ct.c2%2Ct.c3%2Ct.c4%2Ct.c5%2Ct.c10%2Ct.c15%2Ct.c1%2Ct.c6%2Ct.c12%2Ct.c7%2Ct.c13%2Ct.c8%2Ct.c11%2Ct.c16%2Ct.c17%2Ct.c9%2Ct.c14%2Ct.c18%2Ct.c19%2Ct.c20%2Ct.c21%2Ct.c22%2Ct.c23%20from%20st_zzgl_xssqb%20t%0Aleft%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0Awhere%20t.xmqkb_id%3D''%23xmid''%20and%20t.sqsj%3D''%23sqsj''%0Aand%20t.location_address%20%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25''%20%0Aand%20t.xjb_id%20in(select%20xjb_id%20from%20st_zzgl_xssqb%20where%20xmqkb_id%3D''%23xmid''%20and%20sqsj%3Dto_char(to_date(sqsj%2C''YYYY-MM-DD'')-1%2C''YYYY-MM-DD'')%20and%20location_address%20not%20%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25''%20%20%20and%20x.pyccmc%20not%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25'')%0A%23roleWhere%20%20order%20by%20%20dwmc%2Cbjmc%2Cnjmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('当日离校研究生', '今日离校研究生', 'select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.c2%2Ct.location_address%2Ct.c3%2Ct.c4%2Ct.c5%2Ct.c10%2Ct.c15%2Ct.c1%2Ct.c6%2Ct.c12%2Ct.c7%2Ct.c13%2Ct.c8%2Ct.c11%2Ct.c16%2Ct.c17%2Ct.c9%2Ct.c14%2Ct.c18%2Ct.c19%2Ct.c20%2Ct.c21%2Ct.c22%2Ct.c23%20from%20st_zzgl_xssqb%20t%0Aleft%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0Awhere%20t.xmqkb_id%3D''%23xmid''%20and%20t.sqsj%3D''%23sqsj''%0Aand%20t.c2%3D''%E4%B8%8D%E5%9C%A8%E6%A0%A1''%0Aand%20t.xjb_id%20in(select%20xjb_id%20from%20st_zzgl_xssqb%20where%20xmqkb_id%3D''%23xmid''%20and%20sqsj%3Dto_char(to_date(sqsj%2C''YYYY-MM-DD'')-1%2C''YYYY-MM-DD'')%20and%20c2%3D''%E5%9C%A8%E6%A0%A1''%20%20and%20x.pyccmc%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25'')%0A%23roleWhere%20order%20by%20%20dwmc%2Cbjmc%2Cnjmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('汇总表', '总计', 'select%0A''%E6%80%BB%E8%AE%A1''%20as%20name%2C%0Acount(distinct%20x.xh)%20as%20zxssl%0A%2Ccount(distinct%20case%20when%20(z.c2%20%3D%20''%E5%9C%A8%E6%A0%A1'')%20then%20z.xjb_id%20end)%20as%20zxsrs%0A%2Ccount(distinct%20case%20when%20(z.location_address%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20z.xjb_id%20end)%20as%20drzjrs%0A%2Ccount(distinct%20x.xh)-(count(distinct%20case%20when%20(z.location_address%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20z.xjb_id%20end))%20as%20drzjwrs%0A%2Ccount(distinct%20case%20when%20(vt.rn%3D1%20and%20vt.LOCATION_ADDRESS%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20and%20(vt.rn%3D2%20and%20vt.LOCATION_ADDRESS%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20vt.xjb_id%20end%20)%20drfjrs%0A%2Ccount(distinct%20case%20when%20(vt.rn%3D2%20and%20vt.LOCATION_ADDRESS%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20and%20(vt.rn%3D1%20and%20vt.LOCATION_ADDRESS%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20vt.xjb_id%20end%20)%20drljrs%0A%2Ccount(distinct%20case%20when%20(z.c8%20%3D%20''%E6%98%AF'')%20then%20z.xjb_id%20end)%20as%20jkgcrs%0Afrom%20v_sjsb_xjb%20x%20left%20join%20V_ZZGL_XSSQB_LATEST%20z%20on%20x.xh%20%3D%20z.xjb_id%20left%20join%20V_ZZGL_XSSQB_LATEST_TWO%20vt%20on%20x.XH%3Dvt.xjb_id%0Awhere%20PKG_VIEW_PARAM.SET_XMID(''%23xmid'')%3D''%23xmid''%20and%20PKG_VIEW_PARAM.SET_SQSJ(''%23sqsj'')%3D''%23sqsj''');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('汇总表', '汇总名单', 'select%0Ax.dwmc%2C%0Acount(distinct%20x.xh)%20as%20zxssl%0A%2Ccount(distinct%20case%20when%20(z.c2%20%3D%20''%E5%9C%A8%E6%A0%A1'')%20then%20z.xjb_id%20end)%20as%20zxsrs%0A%2Ccount(distinct%20case%20when%20(z.location_address%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20z.xjb_id%20end)%20as%20drzjrs%0A%2Ccount(distinct%20x.xh)-(count(distinct%20case%20when%20(z.location_address%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20z.xjb_id%20end))%20as%20drzjwrs%0A%2Ccount(distinct%20case%20when%20(vt.rn%3D1%20and%20vt.LOCATION_ADDRESS%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20and%20(vt.rn%3D2%20and%20vt.LOCATION_ADDRESS%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20vt.xjb_id%20end%20)%20drfjrs%0A%2Ccount(distinct%20case%20when%20(vt.rn%3D2%20and%20vt.LOCATION_ADDRESS%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20and%20(vt.rn%3D1%20and%20vt.LOCATION_ADDRESS%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25'')%20then%20vt.xjb_id%20end%20)%20drljrs%0A%2Ccount(distinct%20case%20when%20(z.c8%20%3D%20''%E6%98%AF'')%20then%20z.xjb_id%20end)%20as%20jkgcrs%0Afrom%20v_sjsb_xjb%20x%20left%20join%20V_ZZGL_XSSQB_LATEST%20z%20on%20x.xh%20%3D%20z.xjb_id%20left%20join%20V_ZZGL_XSSQB_LATEST_TWO%20vt%20on%20x.XH%3Dvt.xjb_id%0Awhere%20PKG_VIEW_PARAM.SET_XMID(''%23xmid'')%3D''%23xmid''%20and%20PKG_VIEW_PARAM.SET_SQSJ(''%23sqsj'')%3D''%23sqsj''%0Agroup%20by%20x.dwmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('京外本科生名单', '京外本科生', 'select%20sqsj%2Cxjb_id%2Cxm%2Cxb%2Cdwmc%2Cbjmc%2Cnjmc%2Cpyccmc%2Cfdyxm%2Clocation_address%2Cc2%2Cc3%2Cc4%2Cc5%2Cc10%2Cc15%2Cc1%2Cc6%2Cc12%2Cc7%2Cc13%2Cc8%2Cc11%2Cc16%2Cc17%2Cc9%2Cc14%2Cc18%2Cc19%2Cc20%2Cc21%2Cc22%2Cc23%20from%20(select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.location_address%2Ct.c2%2Ct.c3%2Ct.c4%2Ct.c5%2Ct.c10%2Ct.c15%2Ct.c1%2Ct.c6%2Ct.c12%2Ct.c7%2Ct.c13%2Ct.c8%2Ct.c11%2Ct.c16%2Ct.c17%2Ct.c9%2Ct.c14%2Ct.c18%2Ct.c19%2Ct.c20%2Ct.c21%2Ct.c22%2Ct.c23%2C%20ROW_NUMBER()%20OVER(PARTITION%20BY%20t.xjb_id%20ORDER%20BY%20t.sqsj)%20AS%20px%20%0A%20%20%20%20%20%20%20from%20V_ZZGL_XSSQB_LATEST%20t%20left%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0A%20%20%20%20%20%20%20where%20PKG_VIEW_PARAM.SET_XMID(''%23xmid'')%3D''%23xmid''%20and%20PKG_VIEW_PARAM.SET_SQSJ(''%23sqsj'')%3D''%23sqsj''%0A%20%20%20%20%20%20%20and%20location_address%20not%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25''%20%20and%20%20x.pyccmc%20not%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25'')%0Awhere%20px%3D1%20%20%23roleWhere%20order%20by%20sqsj%2Cdwmc%2Cbjmc%2Cnjmc');
INSERT INTO MY_TABLE(NAME, CONFIGNAME, SQLSTR) VALUES ('京内本科生名单', '京内本科生', 'select%20sqsj%2Cxjb_id%2Cxm%2Cxb%2Cdwmc%2Cbjmc%2Cnjmc%2Cpyccmc%2Cfdyxm%2Clocation_address%2Cc2%2Cc3%2Cc4%2Cc5%2Cc10%2Cc15%2Cc1%2Cc6%2Cc12%2Cc7%2Cc13%2Cc8%2Cc11%2Cc16%2Cc17%2Cc9%2Cc14%2Cc18%2Cc19%2Cc20%2Cc21%2Cc22%2Cc23%20from%20(select%20t.sqsj%2Ct.xjb_id%2Cx.xm%2Cx.xb%2Cx.dwmc%2Cx.bjmc%2Cx.njmc%2Cx.pyccmc%2Cx.fdyxm%2Ct.location_address%2Ct.c2%2Ct.c3%2Ct.c4%2Ct.c5%2Ct.c10%2Ct.c15%2Ct.c1%2Ct.c6%2Ct.c12%2Ct.c7%2Ct.c13%2Ct.c8%2Ct.c11%2Ct.c16%2Ct.c17%2Ct.c9%2Ct.c14%2Ct.c18%2Ct.c19%2Ct.c20%2Ct.c21%2Ct.c22%2Ct.c23%2CROW_NUMBER()%20OVER(PARTITION%20BY%20t.xjb_id%20ORDER%20BY%20t.sqsj)%20AS%20px%20%0A%20%20%20%20%20%20%20from%20V_ZZGL_XSSQB_LATEST%20t%20left%20join%20st_xs_xjb%20x%20on%20t.xjb_id%3Dx.xh%0A%20%20%20%20%20%20%20where%20PKG_VIEW_PARAM.SET_XMID(''%23xmid'')%3D''%23xmid''%20and%20PKG_VIEW_PARAM.SET_SQSJ(''%23sqsj'')%3D''%23sqsj''%0A%20%20%20%20%20%20%20and%20location_address%20%20like%20''%25%E5%8C%97%E4%BA%AC%E5%B8%82%25''%20%20and%20%20x.pyccmc%20not%20like%20''%25%E7%A0%94%E7%A9%B6%E7%94%9F%25'')%0Awhere%20px%3D1%20%20%23roleWhere%20order%20by%20sqsj%2Cdwmc%2Cbjmc%2Cnjmc');


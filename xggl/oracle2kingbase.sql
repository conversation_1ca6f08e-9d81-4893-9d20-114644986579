drop type ty_str_split;
DROP FUNCTION IF EXISTS fn_split(varchar, varchar);
CREATE TYPE TY_STR_SPLIT AS (
    value VARCHAR(400)
);
CREATE OR REPLACE FUNCTION fn_split(p_str VARCHAR, p_delimiter VARCHAR)
RETURNS VARCHAR(400)[] AS $$
DECLARE
    j INT := 0;
    i INT := 1;
    len INT := LENGTH(p_str);
    len1 INT := LENGTH(p_delimiter);
    str VARCHAR(4000);
    str_split VARCHAR(400)[] := ARRAY[]::VARCHAR(400)[];
BEGIN
    WHILE i <= len LOOP
        j := POSITION(p_delimiter IN SUBSTRING(p_str FROM i));

        IF j = 0 THEN
            str := SUBSTRING(p_str FROM i);
            str_split := array_append(str_split, str);
            EXIT;
        ELSE
            str := SUBSTRING(p_str FROM i FOR j - 1);
            i := i + j + len1 - 1;
            str_split := array_append(str_split, str);
        END IF;
    END LOOP;

    RETURN str_split;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION fn_split_table(p_str VARCHAR, p_delimiter VARCHAR)
RETURNS TABLE(value VARCHAR) AS $$
BEGIN
    RETURN QUERY SELECT unnest(FN_SPLIT(p_str, p_delimiter));
END;
$$ LANGUAGE plpgsql;

-- oracle版fn_split改动
CREATE OR REPLACE FUNCTION fn_split_table(p_str IN VARCHAR2, p_delimiter IN VARCHAR2)
RETURN ty_str_split PIPELINED IS
    j INT := 0;
    i INT := 1;
    len INT := LENGTH(p_str);
    len1 INT := LENGTH(p_delimiter);
    str VARCHAR2(4000);
BEGIN
    WHILE i <= len LOOP
        j := INSTR(p_str, p_delimiter, i);

        IF j = 0 THEN
            str := SUBSTR(p_str, i);
            PIPE ROW (str);
            EXIT;
        ELSE
            str := SUBSTR(p_str, i, j - i);
            PIPE ROW (str);
            i := j + len1;
        END IF;
    END LOOP;

    RETURN;
END fn_split_table;


select *
from ST_INDEX_MODULE_CONFIG this_
where '本院' in (select * from fn_split_table(role_name, ','))
order by this_.SORT asc;
create view V_RESTRICTIONS_DATA as
select a."XH" ,a."XM" ,a."XB" ,a."MZMC" ,a."DWMC" ,a."ZYMC" ,

       a."BJMC" ,a."NJMC" ,a."XZLXMC" ,a."SFBYS" ,a."ZZMMMC" ,a."PYCCMC" ,a.jg as jiguan,

       a.sfqf ,a.xqmc ,a.sfyzrw as sffby,


       b.bgbzw ,

       c.zhcphz ,

       d.kccjxx ,

       e.nzxytycj ,

       f.tycj ,

       g.tyzt ,

       h.pjcjxx ,

       i.pksxx ,

       j.stxscjb ,

       --k.<PERSON><PERSON>,

       m.stmc ,

       l.tccj ,

       n.syxx ,

       o.stothercpcj ,

       p.dycj ,

       q.hjcstj ,

       r.zcpjcj ,

       s.tycjpjf ,

       t.tccjpjf,

       h1.zhcjdj

from (select xh, xm, xb,mzmc, dwmc,zymc,bjmc, njmc, xzlxmc, sfbys, zzmmmc,pyccmc,sfqf,xqmc, jg,sfyzrw

      from st_xs_xjb) a

         left join (select xh,

                           '[' || listagg(bgbzw, ',') within group(order by bgbzw) || ']' as bgbzw

                    from (select xh, '{"bgbzw":"'||bgbzw||'","spzt":"'||spzt||'","rzsj":"'||rzsj||'","rzjssj":"'||rzjssj||'"}' bgbzw from ST_XS_GBGL

                          where spzt='??')

                    group by xh) b

                   on a.xh = b.xh

         left join (select xh,

                           '[' || listagg(zhcphzb_text, ',') within group(order by zhcphzb_text) || ']' zhcphz

                    from (select xh,

                                 '{"cpnf":"' || cpnf || '","bjpm":"' || bjpm ||

                                 '","bjpmbfb":"' || bjpmbfb || '","njpm":"' || njpm ||

                                 '","njpmbfb":"' || njpmbfb || '","zypm":"' || zypm ||

                                 '","zypmbfb":"' || zypmbfb || '","yxpm":"' || yxpm ||

                                 '","yxpmbfb":"' || yxpmbfb || '","cppm":"' || cppm ||

                                 '","cppmbfb":"' || cppmbfb || '","bjrs":"' || bjrs ||

                                 '","njrs":"' || njrs || '","zyrs":"' || zyrs ||

                                 '","yxrs":"' || yxrs || '","cprs":"' || cprs ||

                                 '","score":"' || score || '"}' as zhcphzb_text

                          from st_zhcp_hzb)

                    group by xh) c

                   on a.xh = c.xh

         left join (select xh,

                           '[' || listagg(score_text, ',') within group(order by score_text) || ']' kccjxx

                    from (select xh,

                                 '{"cpnf":"' || cpnf || '","type":"' || type ||

                                 '","bjpmbfb":"' || bjpmbfb || '","zypmbfb":"' ||

                                 zypmbfb || '","yxpmbfb":"' || yxpmbfb ||

                                 '","sumcj":"' || sumcj || '", "bjpm":"'||bjpm||'","yxpm":"'||yxpm||'","zypm":"'||zypm||'"}' score_text

                          from st_cjgl_score_result)

                    group by xh) d

                   on a.xh = d.xh

         left join (select xh,

                           '['||listagg(nzxytycj, ',') within group(order by nzxytycj)||']' nzxytycj

                    from (select xh, '{"cpnf":"'||cpnf || '","cszf":"' || cszf || '","cjdj":"' || cjdj||'"}' nzxytycj

                          from st_nzxy_tycj)

                    group by xh) e

                   on a.xh = e.xh

         left join (select xh,

                           '['||listagg(tycj, ',') within group(order by tycj)||']' tycj

                    from (select xh, '{"cpnf":"'||cpnf || '","tycj":"' || tycj||'"}' tycj from st_xs_tycjb)

                    group by xh) f

                   on a.xh = f.xh

         left join st_twgl_tyxx g

                   on a.xh = g.xh

         left join (select xh,

                           '[' || listagg(avgtext, ',') within group(order by avgtext) || ']' pjcjxx

                    from (select xh,

                                 '{"type":"' || type || '","score":"' || score || '"}' avgtext

                          from st_zhcp_avg)

                    group by xh) h

                   on a.xh = h.xh



         left join (select xh,

                           '[' || listagg(grade_text, ',') within group(order by grade_text) || ']' zhcjdj

                    from (

                             select tmp.xh,'{"nf":"' || tmp.nf || '","grade":"' || tmp.grade ||'"}' grade_text  from (

                                                                                                                         select xh, nf,

                                                                                                                                case

                                                                                                                                    when zhcjfs >=0 and zhcjfs<60 then '???'

                                                                                                                                    when zhcjfs >=60 and zhcjfs<70 then '????'

                                                                                                                                    when zhcjfs >=70 and zhcjfs<80 then '??'

                                                                                                                                    when zhcjfs >=80 and zhcjfs<90 then '??' else

                                                                                                                                        '??'

                                                                                                                                    end grade from ST_ZHCJ_FSGL) tmp

                         )

                    group by xh) h1

                   on a.xh = h1.xh

         left join (select xjb_id,

                           '[' || listagg(pksxx, ',') within group(order by pksxx) || ']' pksxx

                    from (select sq.xjb_id,

                                 '{"xmid":"' || sq.xmqkb_id || '","pdnf":"' ||

                                 sq.pdnf || '","xssqid":' || sq.id ||

                                 '","lastrole":"' ||

                                 replace(substr(xm.hasrole,

                                                instr(xm.hasrole, ',', -1) + 1),

                                         '_',

                                         ',') || '","columnvalue":"' ||

                                 jgb.columnvalue

                                     || '","kndj":"' || (select sfpks from st_xs_xjb where xh=sq.xjb_id)

                                     || '"}' pksxx

                          from st_zzgl_xssqb      sq,

                               st_zzgl_xmqkb      xm,

                               st_work_flow       wf,

                               st_work_flow_spjgb jgb

                          where sq.xmqkb_id = xm.id

                            and sq.xmqkb_id = wf.xm_id

                            and sq.pdnf = wf.pdnf

                            and sq.type = 'PKSGL'

                            and jgb.xm_id = xm.id

                            and jgb.xssqb_id = sq.id

                            and jgb.flow_id = wf.id

                            and jgb.columnvalue = '??'

                            and jgb.flag = 'spzt'

                            and instr(replace(substr(xm.hasrole,

                                                     instr(xm.hasrole, ',', -1) + 1),

                                              '_',

                                              ','),

                                      jgb.rname) > 0)

                    group by xjb_id) i

                   on a.xh = i.xjb_id

         left join (select xh,

                           '[' || listagg(stxscjb, ',') within group(order by stxscjb) || ']' stxscjb

                    from (select xh,

                                 '{"cpnf":"' || cpnf || '","dektcj":"' || dektcj ||

                                 '","ldjycj":"' || ldjycj || '"}' stxscjb

                          from st_xs_cjb)

                    group by xh) j

                   on a.xh = j.xh

    /*left join (select xh,

                      '[' || listagg(studentscore, ',') within group(order by studentscore) || ']' studentscore

                 from (select xh,

                              '{"xnxq":"' || xnxq || '","kcsxmc":"' || kcsxmc ||

                              '","kccj":"' || kccj || '","cjbz":"'||cjbz||'","xdfsmc":"'||xdfsmc||'"}' studentscore

                         from st_cjgl_student_score)

                group by xh) k

      on a.xh = k.xh*/

         left join (select szzh,

                           '[' || listagg(stmc, ',') within group(order by stmc) || ']' stmc

                    from (select szzh, '"' || stmc || '"' stmc from st_twgl_twst)

                    group by szzh) m

                   on a.xh = m.szzh

         left join (select syzh,

                           '[' || listagg(text, ',') within group(order by text) || ']' syxx

                    from (select syzh,

                                 '{"stmc":"' || stmc || '","zwmc":"' || zwmc || '"}' text

                          from st_twgl_twsy)

                    group by syzh) n

                   on a.xh = n.syzh

         left join (select xh,

                           '[' || listagg(text, ',') within group(order by text) || ']' tccj

                    from (select xh,

                                 '{"ksxq":"' || ksxq || '","score":"' || score || '"}' text

                          from st_xs_tccj)

                    group by xh) l

                   on a.xh = l.xh

         left join (



    select xh,

           '[' || listagg(stothercpcj, ',') within group(order by stothercpcj) || ']' stothercpcj

    from (select xh,

                 '{"cpnf":"' || cpnf || '","cppmbfb":"' ||

                 cppmbfb || '","njpmbfb":"' || njpmbfb ||

                 '","zypmbfb":"' || zypmbfb || '","yxpmbfb":"' ||

                 yxpmbfb || '","bjpmbfb":"' || bjpmbfb || '"}' stothercpcj

          from st_other_cpcj)

    group by xh) o

                   on a.xh = o.xh

         left join (select xh,

                           '[' || listagg(text, ',') within group(order by text) || ']' dycj

                    from (select xh,

                                 '{"cpnf":"' || cpnf || '","cj":"' || cj || '"}' text

                          from st_xs_dycjb)

                    group by xh) p

                   on a.xh = p.xh left join (select xh, '{"shxshjcs":"'||

                                                        max(decode(xmmc, '????',total)) ||'","yxxsgbhjcs":"'||

                                                        max(decode(xmmc, '??????',total)) ||'","yxgqtyhjcs":"'||

                                                        max(decode(xmmc, '??????',total)) ||'","yxgqtgb":"'||

                                                        max(decode(xmmc, '???????',total)) ||'"}' hjcstj

                                             from (

                                                      select res.xh, xm.xmmc, count(*) total

                                                      from st_zzgl_xssq_spjgb res, st_zzgl_xmqkb xm

                                                      where res.xmid = xm.id

                                                        and xm.xmmc in ('????','??????','??????','???????')

                                                      group by res.xh, xm.xmmc) group by xh) q on a.xh = q.xh

         left join (select xh, round(avg(score),2) zcpjcj from st_zhcp_hzb group by xh) r

                   on a.xh = r.xh

         left join (select xh, round(avg(tycj),2) tycjpjf from st_xs_tycjb group by xh) s

                   on a.xh = s.xh

         left join (select xh, round(avg(score),2) tccjpjf from st_xs_tccj group by xh) t

                   on a.xh = t.xh
/


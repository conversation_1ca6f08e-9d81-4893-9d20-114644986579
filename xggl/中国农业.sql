--20240531
alter table ST_FDYKH_KPJL
add ZGPJ varchar2(2000)
/
comment on column ST_FDYKH_KPJL.ZGPJ is '主观评价'
/

alter table ST_XS_XJB
    add SFZDGZ varchar2(50)
/

comment on column ST_XS_XJB.SFZDGZ is '是否重点关注'
/





-- 资助关联学籍异动，学籍异动，资助项目，（今年的9.1-1.31）秋季异动过，流程会发送到学籍异动前对应的班级、院系。（2.1-8.31）春季学期异动，不需要调用学籍异动
create or replace view v_xs_xjyd as
select x.xh,
       x.xm,
       case
           when b.YDSJ >= TO_DATE(TO_CHAR(sysdate, 'YYYY') || '-09-01', 'YYYY-MM-DD')
               AND b.YDSJ <= TO_DATE(TO_CHAR(sysdate + INTERVAL '1' YEAR, 'YYYY') || '-01-31', 'YYYY-MM-DD')
               then b.YDQDW
           else x.DWMC end dwmc,
       case
           when b.YDSJ >= TO_DATE(TO_CHAR(sysdate, 'YYYY') || '-09-01', 'YYYY-MM-DD')
               AND b.YDSJ <= TO_DATE(TO_CHAR(sysdate + INTERVAL '1' YEAR, 'YYYY') || '-01-31', 'YYYY-MM-DD')
               then b.YDQZY
           else x.ZYMC end zymc,
       case
           when b.YDSJ >= TO_DATE(TO_CHAR(sysdate, 'YYYY') || '-09-01', 'YYYY-MM-DD')
               AND b.YDSJ <= TO_DATE(TO_CHAR(sysdate + INTERVAL '1' YEAR, 'YYYY') || '-01-31', 'YYYY-MM-DD')
               then b.YDQBJ
           else x.BJMC end bjmc
from ST_XS_XJB x
         left join ST_XS_XJYDB b on x.XH = b.XJB_ID;
DECLARE
    v_users VARCHAR2(4000);
    v_username VARCHAR2(100);
    v_sql VARCHAR2(1000);
BEGIN
    -- 设置用户列表
    v_users := 'syt_bddlsjk,syt_cczyyxg,syt_cqcsxy,syt_cxcy_hbrj,syt_datacenter_new,syt_dcwj,syt_dev_platform,syt_dzdxxg,syt_flowable,syt_gsh,syt_hbwy,syt_hbwymh,syt_hlbemh,syt_ht_lxxt,syt_ht_mh,syt_ht_sgxt,syt_ht_thaus,syt_ht_xg,syt_imunmh,syt_job_bddl,syt_job_cqcsxy,syt_job_hbdwjm,syt_job_hbrj,syt_job_nzxy,syt_job_xsbn,syt_job_xsbn_new,syt_job_zgzf,syt_labour,syt_lssf_ysxg,syt_lxxt,syt_machine,syt_mange_sys,syt_new_portal,syt_new_portal_bddl,syt_new_zsxt,syt_new_zsxt_wuhan,syt_ngd_zs,syt_ngtz,syt_nh,syt_nhxg,syt_nhxg0331,syt_nzxg,syt_oauth,syt_oldxg_test,syt_report,syt_scaffold,syt_sjw_trans,syt_ssgl,syt_thaus_new,syt_tsxg,syt_wzxyyx,syt_wzxy_ssgl,syt_wzyx,syt_xggl_bjlb,syt_xggl_gdxy,syt_xgxt_btqg,syt_xgxt_zgnydx,syt_xgxt_zyykdx,syt_xg_bddl,syt_xg_bjlbzyxy,syt_xg_lssf,syt_xg_nmnd,syt_xg_nzxy,syt_xg_yncjdx,syt_xsbnmh,syt_xxkdmh,syt_xxkd_yqxt,syt_xxkjxg,syt_xyxt,syt_xyxt_hbrj,syt_ybxy,syt_yjsxt,syt_ynzyy,syt_yqxt,syt_yx_nmjy,syt_yx_nmmd,syt_zsjy,syt_zsxt,syt_zsxttz_hbrj,syt_zsxt_bddl,syt_zsxt_hbmy,syt_zsxt_hbrj,syt_zsxt_lssf,syt_zsxt_ngd,syt_zsxt_nzxy,syt_zsxt_syxy,syt_zsxt_tz,syt_zsxt_xsbn,syt_zsxt_zycj';

    FOR i IN (
        SELECT REGEXP_SUBSTR(v_users, '[^,]+', 1, LEVEL) AS user_name
        FROM dual
        CONNECT BY REGEXP_SUBSTR(v_users, '[^,]+', 1, LEVEL) IS NOT NULL
    ) LOOP
        v_username := LOWER(TRIM(i.user_name));  -- 建议用小写，避免大小写问题

        BEGIN
            -- 删除用户
            v_sql := 'DROP USER ' || v_username || ' CASCADE';
            EXECUTE IMMEDIATE v_sql;
        EXCEPTION
            WHEN OTHERS THEN
                NULL; -- 用户不存在则跳过
        END;

        BEGIN
            -- 创建用户
            v_sql := 'CREATE USER ' || v_username || ' IDENTIFIED BY ' || v_username || '_2025';
            EXECUTE IMMEDIATE v_sql;

            -- 基础权限
            EXECUTE IMMEDIATE 'GRANT CONNECT, RESOURCE TO ' || v_username;

            -- 创建对象权限
            EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO ' || v_username;
            EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO ' || v_username;
            EXECUTE IMMEDIATE 'GRANT CREATE FUNCTION TO ' || v_username;
            EXECUTE IMMEDIATE 'GRANT CREATE SEQUENCE TO ' || v_username;
            EXECUTE IMMEDIATE 'GRANT CREATE SYNONYM TO ' || v_username;
            EXECUTE IMMEDIATE 'GRANT CREATE TRIGGER TO ' || v_username;
            EXECUTE IMMEDIATE 'GRANT CREATE PACKAGE TO ' || v_username;

            -- 表空间配额（根据你的导入表空间情况调整）
            EXECUTE IMMEDIATE 'ALTER USER ' || v_username || ' QUOTA UNLIMITED ON USERS';
            EXECUTE IMMEDIATE 'ALTER USER ' || v_username || ' QUOTA UNLIMITED ON SYT_DATA';
            EXECUTE IMMEDIATE 'ALTER USER ' || v_username || ' QUOTA UNLIMITED ON SYT_DCWJ';
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('创建失败：' || v_username || ' => ' || SQLERRM);
        END;
    END LOOP;
END;
/



SELECT TABLESPACE_NAME, FILE_NAME FROM DBA_DATA_FILES ORDER BY TABLESPACE_NAME;

-- SYT_DATA 表空间
CREATE TABLESPACE SYT_DATA
DATAFILE '/home/<USER>/oradata/syt_data01.dbf'
SIZE 500M AUTOEXTEND ON NEXT 100M MAXSIZE UNLIMITED
EXTENT MANAGEMENT LOCAL;

-- SYT_DCWJ 表空间
CREATE TABLESPACE SYT_DCWJ
DATAFILE '/home/<USER>/oradata/syt_dcwj01.dbf'
SIZE 500M AUTOEXTEND ON NEXT 100M MAXSIZE UNLIMITED
EXTENT MANAGEMENT LOCAL;

CREATE OR REPLACE TYPE TY_STR_SPLIT AS TABLE OF VARCHAR2(400);


-- imp syt_xgxt/Sanyth_2025# file=6.dmp
-- fromuser=syt_oldxg_test,syt_zsxt_tz,syt_job_zgzf,syt_datacenter_new,syt_dev_platform,syt_ht_mh,syt_ht_xg,syt_new_portal,syt_new_zsxt,syt_oauth,syt_report,syt_xxkdmh,syt_xyxt,syt_xyxt_hbrj
-- touser=syt_oldxg_test,syt_zsxt_tz,syt_job_zgzf,syt_datacenter_new,syt_dev_platform,syt_ht_mh,syt_ht_xg,syt_new_portal,syt_new_zsxt,syt_oauth,syt_report,syt_xxkdmh,syt_xyxt,syt_xyxt_hbrj  ignore=Y


ALTER USER syt_ht_xg IDENTIFIED BY syt201610syt_ht_xg;
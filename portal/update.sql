--1008
alter table SYT_MESSAGE
    add URL varchar2(255)
/

comment on column SYT_MESSAGE.URL is '网址'
/



--0620
alter table SYT_APPLET
    add PLACEMENT varchar2(255)
/

comment on column SYT_APPLET.PLACEMENT is '展示位置'
/

alter table SYT_APPLET
    add DISPLAY_MODE varchar2(255)
/

comment on column SYT_APPLET.PLACEMENT is '展示类型'
/



--0608
alter table SYT_MSG
    drop column ORG_ID
/

alter table SYT_MSG
    drop column ROLE_ID
/

alter table SYT_MSG
    add USERIDS varchar2(4000)
/

alter table SYT_MSG
    add ORGIDS varchar2(4000)
/

alter table SYT_MSG
    add ROLEIDS varchar2(4000)
/
alter table SYT_MSG
    add ISADMIN_PUSH varchar2(255)
/

comment on column SYT_MSG.ISADMIN_PUSH is '管理员发布'
/



-- 0531
alter table SYT_FILE_RESOURCE
    add ROLEIDS varchar2(4000)
/

comment on column SYT_FILE_RESOURCE.ROLEIDS is '角色'
/
alter table SYT_FILE_RESOURCE
    add ORGIDS varchar2(4000)
/

comment on column SYT_FILE_RESOURCE.ORGIDS is '组织机构'
/



--0523
alter table SYT_DESKTOP_BLOCK
    add MOBILE_MORE_URL varchar2(255)
/

comment on column SYT_DESKTOP_BLOCK.MOBILE_MORE_URL is '移动端更多地址'
/

alter table SYT_DATA_CATEGORY
    add MOBILE_MORE_URL varchar2(255)
/

comment on column SYT_DATA_CATEGORY.MOBILE_MORE_URL is '移动端更多地址'
/


--0509
alter table SYT_DESKTOP_BLOCK
    modify ORG_ID VARCHAR2(4000 char)
/

alter table SYT_DESKTOP_BLOCK
    modify ROLE_ID VARCHAR2(4000 char)
/


-- 0508
alter table OAUTH_CLIENT_DETAILS
    add auth_type varchar2(255)
/

comment on column OAUTH_CLIENT_DETAILS.auth_type is '认证类型'
/

alter table OAUTH_CLIENT_DETAILS
    add thauth_appid varchar2(255)
/

comment on column OAUTH_CLIENT_DETAILS.thauth_appid is 'thauth_appid'
/

-- 0410
alter table BPM_CUSTOM_COLUMN
    add FIELD_TYPE varchar2(255)
/

comment on column BPM_CUSTOM_COLUMN.FIELD_TYPE is '字段类型'
/



alter table SYT_DESKTOP
    add VISITOR_VISIBILITY varchar2(255)
/

comment on column SYT_DESKTOP.VISITOR_VISIBILITY is '访客可见'
/
alter table SYT_DESKTOP_BLOCK
    add VISITOR_VISIBILITY varchar2(255)
/

comment on column SYT_DESKTOP_BLOCK.VISITOR_VISIBILITY is '访客可见'
/

alter table SYT_DESKTOP_APP
    add VISITOR_VISIBILITY varchar2(255)
/

comment on column SYT_DESKTOP_APP.VISITOR_VISIBILITY is '游客可见'
/

alter table SYT_SERVICE_CENTER
    add VISITOR_VISIBILITY varchar2(255)
/

comment on column SYT_SERVICE_CENTER.VISITOR_VISIBILITY is '访客可见'
/
alter table SYT_PERMISSION_RESOURCE
    add VISITOR_VISIBILITY varchar2(255)
/

comment on column SYT_PERMISSION_RESOURCE.VISITOR_VISIBILITY is '游客可见'
/

INSERT INTO SYT_PERMISSION_ACCOUNT (ID, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, ORGANIZATIONNAMES, JSSJ, KSSJ, ACTIVEFLAG, EMPLOYEETYPE, ORGSHORTNAME, MODIFYPASSTIME, AVATARID, AVATARURL) VALUES ('syt_visitor', 'syt_visitor', null, '访客', null, TIMESTAMP '2022-11-21 10:24:51', DATE '2022-11-21', DATE '2099-12-31', 0, 'c31f2a24b078ae1f359f671343abc0be', null, null, null, null, '**********', null, null, 'dsds', null, null, null, null, null, '111111111111111111', '居民身份证', TIMESTAMP '2023-03-23 11:54:26', null, null, null, 0, null, null, null, null, null, 'null', TIMESTAMP '2022-11-23 16:43:18', null, null);
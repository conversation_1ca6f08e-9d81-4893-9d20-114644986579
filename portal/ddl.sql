create table SYT_NEW_PORTAL.OAUTH_ACCESS_TOKEN
(
    TOKEN_ID          NVARCHAR2(256),
    TOKEN             BLOB,
    AUTHENTICATION_ID NVARCHAR2(255) not null
        constraint SYS_C0020380
        primary key
        constraint SYS_C0017997
        check ("AUTHENTICATION_ID" IS NOT NULL)
        constraint SYS_C0020364
        check ("AUTHENTICATION_ID" IS NOT NULL),
    USER_NAME         NVARCHAR2(256),
    CLIENT_ID         NVARCHAR2(256),
    AUTHENTICATION    BLOB,
    REFRESH_TOKEN     NVARCHAR2(256)
)
    /

create table SYT_NEW_PORTAL.OAUTH_APPROVALS
(
    USERID         NVARCHAR2(256),
    CLIENTID       NVARCHAR2(256),
    SCOPE          NVARCHAR2(256),
    STATUS         NVARCHAR2(10),
    EXPIRESAT      DATE not null
        constraint SYS_C0017998
            check ("EXPIRESAT" IS NOT NULL)
        constraint SYS_C0020365
            check ("EXPIRESAT" IS NOT NULL),
    LASTMODIFIEDAT DATE
)
    /

create table SYT_NEW_PORTAL.OAUTH_CLIENT_DETAILS
(
    ID                      VARCHAR2(32)  not null
        constraint SYS_C0020381
        primary key
        constraint SYS_C0017999
        check ("ID" IS NOT NULL)
        constraint SYS_C0020366
        check (id IS NOT NULL)
        constraint SYS_C0020421
        check ("ID" IS NOT NULL),
    CLIENT_ID               VARCHAR2(255) not null
        constraint SYS_C0018000
        check ("CLIENT_ID" IS NOT NULL)
        constraint SYS_C0020367
        check (client_id IS NOT NULL)
        constraint SYS_C0020422
        check ("CLIENT_ID" IS NOT NULL),
    RESOURCE_IDS            VARCHAR2(256),
    CLIENT_SECRET           VARCHAR2(256),
    SCOPE                   VARCHAR2(256),
    AUTHORIZED_GRANT_TYPES  VARCHAR2(256),
    AUTHORITIES             VARCHAR2(256),
    ACCESS_TOKEN_VALIDITY   NUMBER(11),
    REFRESH_TOKEN_VALIDITY  NUMBER(11),
    AUTOAPPROVE             VARCHAR2(256),
    CLIENT_NAME             VARCHAR2(255),
    CREATE_TIME             DATE,
    WEB_SERVER_REDIRECT_URI VARCHAR2(1000),
    ADDITIONAL_INFORMATION  VARCHAR2(1000),
    REMARK                  VARCHAR2(1024),
    CLIENT_INDEX            VARCHAR2(255 char),
    DESCRIPTION             VARCHAR2(255),
    STATUS                  VARCHAR2(255 char),
    SORT                    NUMBER(10),
    ROLEIDS                 CLOB,
    ORGIDS                  CLOB,
    CLIENT_ICON             VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.OAUTH_CLIENT_TOKEN
(
    TOKEN_ID          NVARCHAR2(256),
    TOKEN             BLOB,
    AUTHENTICATION_ID NVARCHAR2(255) not null
        constraint SYS_C0020382
        primary key
        constraint SYS_C0018001
        check ("AUTHENTICATION_ID" IS NOT NULL)
        constraint SYS_C0020368
        check ("AUTHENTICATION_ID" IS NOT NULL),
    USER_NAME         NVARCHAR2(256),
    CLIENT_ID         NVARCHAR2(256)
)
    /

create table SYT_NEW_PORTAL.OAUTH_CODE
(
    CODE           NVARCHAR2(256),
    AUTHENTICATION BLOB
)
    /

create table SYT_NEW_PORTAL.OAUTH_REFRESH_TOKEN
(
    TOKEN_ID       NVARCHAR2(256),
    TOKEN          BLOB,
    AUTHENTICATION BLOB
)
    /

create table SYT_NEW_PORTAL.SYS_USER
(
    ID              NUMBER(20)     not null
        constraint SYS_C0020385
        primary key
        constraint SYS_C0018033
        check ("ID" IS NOT NULL)
        constraint SYS_C0020375
        check ("ID" IS NOT NULL),
    USERNAME        NVARCHAR2(255) not null
        constraint SYS_C0018034
        check ("USERNAME" IS NOT NULL)
        constraint SYS_C0020376
        check ("USERNAME" IS NOT NULL),
    PASSWORD        NVARCHAR2(255) not null
        constraint SYS_C0018035
        check ("PASSWORD" IS NOT NULL)
        constraint SYS_C0020377
        check ("PASSWORD" IS NOT NULL),
    REAL_NAME       NVARCHAR2(255),
    STATUS          NUMBER(4),
    LAST_LOGIN_TIME DATE,
    PHONE           NVARCHAR2(255),
    EXPIRED         NUMBER(4),
    DISABLED        NUMBER(4)
)
    /

comment on column SYT_NEW_PORTAL.SYS_USER.STATUS is '0：正常，1：冻结'
/

comment on column SYT_NEW_PORTAL.SYS_USER.EXPIRED is '0：正常，1：为过期(需修改密码)'
/

comment on column SYT_NEW_PORTAL.SYS_USER.DISABLED is '0：正常，1：不可用(禁用)'
/

create table SYT_NEW_PORTAL.SYT_CODE_JSLXB
(
    ID   VARCHAR2(50 char) not null
        constraint SYS_C0018292
        check ("ID" IS NOT NULL),
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_CODE_MZB
(
    ID   VARCHAR2(50 char) not null
        constraint SYS_C0018293
        check ("ID" IS NOT NULL),
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_CODE_XQB
(
    ID   VARCHAR2(50 char) not null
        constraint SYS_C0018294
        check ("ID" IS NOT NULL),
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char),
    XQDZ VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_CODE_ZZMMB
(
    ID   VARCHAR2(50 char) not null
        constraint SYS_C0018295
        check ("ID" IS NOT NULL),
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_DATA_CATEGORY
(
    ID           VARCHAR2(255 char) not null
        constraint SYS_C0018296
        check ("ID" IS NOT NULL),
    CREATE_TIME  TIMESTAMP(6),
    NAME         VARCHAR2(255 char),
    REMARK       VARCHAR2(255 char),
    MODEL        VARCHAR2(255 char),
    SORT         NUMBER(10),
    BLOCK_ID     VARCHAR2(255 char),
    DESKTOP_ID   VARCHAR2(255 char),
    DISPLAY      VARCHAR2(255 char),
    ICON         VARCHAR2(255 char),
    PATH         VARCHAR2(255 char),
    TYPE         VARCHAR2(255 char),
    VALUE        VARCHAR2(255 char),
    CLICK_URL    VARCHAR2(255 char),
    DATA_SOURCES VARCHAR2(255 char),
    MORE_URL     VARCHAR2(255 char)
)
    /

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.ID is '主键'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.CREATE_TIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.NAME is '选项卡名称'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.REMARK is '备注'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.MODEL is '所属模块'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.DESKTOP_ID is '桌面ID'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.DISPLAY is '是否显示'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.ICON is '图标'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.PATH is '路径'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.TYPE is '数据类型'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.CLICK_URL is '点击地址'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.DATA_SOURCES is '数据来源'
/

comment on column SYT_NEW_PORTAL.SYT_DATA_CATEGORY.MORE_URL is '更多地址'
/

create table SYT_NEW_PORTAL.SYT_DESKTOP
(
    ID                 VARCHAR2(255 char) not null
        constraint SYS_C0018297
        check ("ID" IS NOT NULL),
    ACCOUNT_ID         VARCHAR2(255 char),
    NAME               VARCHAR2(255 char),
    ORG_ID             VARCHAR2(255 char),
    REMARK             VARCHAR2(255 char),
    ROLE_ID            VARCHAR2(255 char),
    ICON               VARCHAR2(255 char),
    SORT               NUMBER(10),
    EDITABLE           VARCHAR2(255),
    TYPE               VARCHAR2(255 char),
    VISITOR_VISIBILITY VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.ID is '主键'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.ACCOUNT_ID is '账号ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.ORG_ID is '组织机构ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.REMARK is '备注'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.ROLE_ID is '角色ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.ICON is '图标'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.EDITABLE is '是否可编辑'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP.VISITOR_VISIBILITY is '访客可见'
/

create table SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK
(
    ID                 VARCHAR2(255 char) not null
        constraint SYS_C0018298
        check ("ID" IS NOT NULL),
    DESKTOP_ID         VARCHAR2(255 char),
    HEIGHT             NUMBER(10),
    NAME               VARCHAR2(255 char),
    ORG_ID             VARCHAR2(255 char),
    REMARK             VARCHAR2(255 char),
    ROLE_ID            VARCHAR2(255 char),
    SORT               NUMBER(10),
    STATUS             VARCHAR2(255 char),
    STYLE              VARCHAR2(255 char),
    MODEL              VARCHAR2(255 char),
    LIMIT              NUMBER(10),
    FRONT_CONFIG       VARCHAR2(4000),
    CLICK_URL          VARCHAR2(255 char),
    DATA_SOURCES       VARCHAR2(255 char),
    MORE_URL           VARCHAR2(255 char),
    TYPE               VARCHAR2(255),
    LUNBOTU_TYPE       VARCHAR2(255 char),
    VISITOR_VISIBILITY VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.DESKTOP_ID is '桌面ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.NAME is '版块名称'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.ORG_ID is '可见组织机构'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.REMARK is '描述'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.ROLE_ID is '可见角色'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.STATUS is '状态'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.STYLE is '风格'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.MODEL is '模块'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.LIMIT is '限制'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.FRONT_CONFIG is '前端组件配置'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.CLICK_URL is '点击地址'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.DATA_SOURCES is '数据源'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.MORE_URL is '更多地址'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.TYPE is '板块类型(内置,可选)'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.LUNBOTU_TYPE is '轮播图类型'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK.VISITOR_VISIBILITY is '访客可见'
/

create table SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_TAB
(
    ID          VARCHAR2(32 char) not null
        constraint SYS_C0018299
        check ("ID" IS NOT NULL),
    BLOCK_ID    VARCHAR2(32 char),
    CATEGORY_ID VARCHAR2(32 char)
)
    /

create table SYT_NEW_PORTAL.SYT_MSG
(
    ID           VARCHAR2(255 char) not null
        constraint SYS_C0018300
        check ("ID" IS NOT NULL),
    CATEGORY_ID  VARCHAR2(255 char),
    CONTENT      VARCHAR2(4000 char),
    CREATE_TIME  TIMESTAMP(6),
    ORG_ID       VARCHAR2(255 char),
    ROLE_ID      VARCHAR2(255 char),
    TITLE        VARCHAR2(255 char),
    USER_ID      VARCHAR2(255 char),
    USER_NAME    VARCHAR2(255 char),
    ATTACH       VARCHAR2(512 char),
    DETAIL_LINKS VARCHAR2(255 char),
    ORG_NAME     VARCHAR2(255 char),
    MSG_TYPE     VARCHAR2(255 char),
    ICON         VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT
(
    ID                VARCHAR2(50) not null
        constraint SYS_C0018301
        check ("ID" IS NOT NULL),
    HUMANCODE         VARCHAR2(50),
    HUMANSMSCODE      VARCHAR2(50),
    HUMANNAME         VARCHAR2(50),
    HUMANDESCRIPTION  VARCHAR2(500),
    CREATEDATE        DATE,
    VALIDFROMDATE     DATE,
    VALIDTODATE       DATE,
    VALIDFLAG         NUMBER default 0,
    HUMANPASSWORD     VARCHAR2(50),
    SEX               VARCHAR2(50),
    BIRTHDAY          DATE,
    TELOFFICE         VARCHAR2(50),
    TELHOME           VARCHAR2(50),
    TELMOBILE1        VARCHAR2(50),
    TELMOBILE2        VARCHAR2(50),
    EMAIL             VARCHAR2(50),
    ADDRESS           VARCHAR2(500),
    POSTALCODE        VARCHAR2(50),
    AGE               NUMBER(6),
    ORGID             VARCHAR2(50),
    SIGNATURE         VARCHAR2(500),
    ENCRYPTYPE        VARCHAR2(50),
    IDCODE            VARCHAR2(50),
    IDTYPE            VARCHAR2(50),
    LOGINTIME         DATE,
    LOGININFO         VARCHAR2(50),
    DUTYID            VARCHAR2(50),
    HUMANNUMBER       VARCHAR2(50),
    DISPLAYORDER      NUMBER,
    ORGANIZATIONNAMES VARCHAR2(100),
    JSSJ              VARCHAR2(200),
    KSSJ              VARCHAR2(200),
    ACTIVEFLAG        NUMBER(10),
    EMPLOYEETYPE      VARCHAR2(100),
    ORGSHORTNAME      VARCHAR2(100),
    MODIFYPASSTIME    DATE,
    AVATARID          VARCHAR2(255 char),
    AVATARURL         VARCHAR2(255 char),
    CURRENTSTATE      VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT is '用户信息表'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.ID is '人员标识（人员工号，可以输入，也可以是系统自动生成的UUID）'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.HUMANCODE is '人员代码，登录账号'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.HUMANSMSCODE is '短信标识号'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.HUMANNAME is '人员名称'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.HUMANDESCRIPTION is '人员描述'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.CREATEDATE is '建立日期'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.VALIDFROMDATE is '有效起始时间'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.VALIDTODATE is '有效终止时间'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.VALIDFLAG is '有效标志0.有效1.离职'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.HUMANPASSWORD is '口令/登录密码'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.SEX is '性别'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.BIRTHDAY is '生日'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.TELOFFICE is '办公室电话号码'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.TELHOME is '家庭电话号码'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.TELMOBILE1 is '手机号码1'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.TELMOBILE2 is '手机号码2'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.EMAIL is '电子邮件'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.ADDRESS is '地址'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.POSTALCODE is '邮编'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.AGE is '年龄-********-修改为排序标识'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.ORGID is '单位标识，多个,号隔开'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.SIGNATURE is '签名/个性签名'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.ENCRYPTYPE is '密码加密类型1:无加密 2:MD5 3:Base64'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.IDCODE is '证件值'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.IDTYPE is '证件类型'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.LOGINTIME is '最近登录时间'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.LOGININFO is '最近登录信息，如IP地址'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.DUTYID is '职务id'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.HUMANNUMBER is '用户工号（多个工程共用一个数据库是做唯一标识）'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.DISPLAYORDER is '显示顺序'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.ORGANIZATIONNAMES is '用户所属机构名称'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.ORGSHORTNAME is '组织机构简称'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.MODIFYPASSTIME is '最近修改密码时间'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT.CURRENTSTATE is '用户当前状态(对应人员前状态码表，比如北信科：离职、退休、在职、返聘)'
/

create table SYT_NEW_PORTAL.SYT_PERMISSION_ACCOUNT_ROLE
(
    ID         VARCHAR2(50) not null
        constraint SYS_C0018302
        check ("ID" IS NOT NULL),
    ROLE_ID    VARCHAR2(50) default NULL,
    ACCOUNT_ID VARCHAR2(50) default NULL,
    ACCOUNTID  VARCHAR2(255 char),
    ROLEID     VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_PERMISSION_RESOURCE
(
    ID                 VARCHAR2(32) not null
        constraint SYS_C0018303
        check ("ID" IS NOT NULL),
    LABEL              VARCHAR2(100) default NULL,
    IS_BTN             VARCHAR2(10)  default '否',
    ICON               VARCHAR2(255) default NULL,
    PARAM              VARCHAR2(255) default NULL,
    PATH               VARCHAR2(255) default NULL,
    PARENT_ID          VARCHAR2(32)  default NULL,
    STATUS             VARCHAR2(10)  default NULL,
    DESCRIPTION        VARCHAR2(255) default NULL,
    URL                VARCHAR2(255) default NULL,
    COMPONENT          VARCHAR2(255) default NULL,
    TYPE               NUMBER(10),
    SORT               NUMBER(10),
    COMPONENTAPP       VARCHAR2(255) default NULL,
    VISITOR_VISIBILITY VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_RESOURCE.VISITOR_VISIBILITY is '游客可见'
/

create table SYT_NEW_PORTAL.SYT_PERMISSION_RESOURCE_ROLE
(
    ID          VARCHAR2(32) not null
        constraint SYS_C0018304
        check ("ID" IS NOT NULL),
    ROLE_ID     VARCHAR2(32) default NULL,
    RESOURCE_ID VARCHAR2(32) default NULL
)
    /

create table SYT_NEW_PORTAL.SYT_PERMISSION_ROLE
(
    ID           VARCHAR2(50) not null
        constraint SYS_C0018305
        check ("ID" IS NOT NULL),
    ROLENAME     VARCHAR2(150),
    ROLEKEY      VARCHAR2(150),
    ROLEINFO     VARCHAR2(1500),
    MASTERID     VARCHAR2(50),
    OPDATE       DATE,
    AUTHSTRING   CLOB,
    MOBILEAUTH   VARCHAR2(255),
    DISPLAYORDER NUMBER,
    ROLETYPE     VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.ID is '主键ID'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.ROLENAME is '角色名称'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.ROLEKEY is '角色唯一标识'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.ROLEINFO is '角色备注'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.MASTERID is '创建者ID'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.OPDATE is '权限串'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.AUTHSTRING is '更新时间'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.MOBILEAUTH is '手机图标角色控制'
/

comment on column SYT_NEW_PORTAL.SYT_PERMISSION_ROLE.DISPLAYORDER is '显示顺序'
/

create table SYT_NEW_PORTAL.SYT_ROLL_BANNER
(
    ID           VARCHAR2(255 char) not null
        constraint SYS_C0018306
        check ("ID" IS NOT NULL),
    CREATE_TIME  TIMESTAMP(6),
    DETAIL       VARCHAR2(255 char),
    IMG          VARCHAR2(255 char),
    ROLE_ID      VARCHAR2(255 char),
    SORT         NUMBER(10),
    STATUS       VARCHAR2(255 char),
    TITLE        VARCHAR2(255 char),
    URL          VARCHAR2(255 char),
    LUNBOTU_TYPE VARCHAR2(255 char)
)
    /

comment on column SYT_NEW_PORTAL.SYT_ROLL_BANNER.LUNBOTU_TYPE is '轮播图类型'
/

create table SYT_NEW_PORTAL.SYT_SERVICE_CENTER
(
    ID                  VARCHAR2(255 char) not null
        constraint SYS_C0018307
        check ("ID" IS NOT NULL),
    CATEGORY_ID         VARCHAR2(255 char),
    COLLECT_NUMBER      NUMBER(19),
    CREATETIME          TIMESTAMP(6),
    ICON                VARCHAR2(255 char),
    LINK                VARCHAR2(255 char),
    NAME                VARCHAR2(255 char),
    REMARK              VARCHAR2(255 char),
    VISITS              NUMBER(19),
    RECOMMEND           VARCHAR2(255 char),
    APP_ID              VARCHAR2(255 char),
    PINYIN              VARCHAR2(255 char),
    SORT                NUMBER(19),
    LINKTYPE            VARCHAR2(255 char),
    PROCESSDEFINITIONID VARCHAR2(255 char),
    ORGID               VARCHAR2(255),
    ORGNAME             VARCHAR2(255 char),
    RESOURCEID          VARCHAR2(255),
    TASKID              VARCHAR2(255),
    VISITOR_VISIBILITY  VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.LINKTYPE is '服务链接类型，区分是工作流，还是其他'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.PROCESSDEFINITIONID is '工作流程定义id'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.ORGID is '所属部门'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.RESOURCEID is '资源服务id'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.TASKID is '任务填报ID'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER.VISITOR_VISIBILITY is '访客可见'
/

create table SYT_NEW_PORTAL.SYT_SERVICE_CENTER_CATEGORY
(
    ID   VARCHAR2(50 char) not null
        constraint SYS_C0018308
        check ("ID" IS NOT NULL),
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_SERVICE_CENTER_COLLECT
(
    ID                VARCHAR2(32) not null
        constraint SYS_C0018309
        check ("ID" IS NOT NULL),
    SERVICE_CENTER_ID VARCHAR2(32),
    ACCOUNT_ID        VARCHAR2(32)
)
    /

create table SYT_NEW_PORTAL.SYT_SERVICE_CENTER_FEEDBACK
(
    ID         VARCHAR2(32) not null
        constraint SYS_C0018310
        check ("ID" IS NOT NULL),
    SERVICE_ID VARCHAR2(32),
    CONTENT    VARCHAR2(1024),
    ACCOUNT_ID VARCHAR2(32),
    CREATETIME DATE
)
    /

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_FEEDBACK.SERVICE_ID is '服务id'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_FEEDBACK.CONTENT is '意见反馈内容'
/

create table SYT_NEW_PORTAL.SYT_SERVICE_CENTER_ROLE
(
    ID         VARCHAR2(255 char) not null
        constraint SYS_C0018311
        check ("ID" IS NOT NULL),
    ROLE_ID    VARCHAR2(255 char),
    SERVICE_ID VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION
(
    ID              VARCHAR2(50) not null
        constraint SYS_C0018313
        check ("ID" IS NOT NULL),
    CODE            VARCHAR2(50),
    ORGNAME         VARCHAR2(100),
    VALID           CHAR,
    DISPLAYORDER    NUMBER,
    ORGDESCRIPTION  VARCHAR2(1500),
    CATEGORY_ID     VARCHAR2(50),
    PARENT          VARCHAR2(50),
    PARENTS         VARCHAR2(2000),
    ORGCODE         VARCHAR2(50),
    ORGSHORTNAME    VARCHAR2(100),
    ORGHEADHUMACODE VARCHAR2(255),
    ORGHEADHUMANAME VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION is '组织机构表'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.ID is '机构ID'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.CODE is '机构编码'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.ORGNAME is '机构名称'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.VALID is '是否有效(0:无效，1:有效)'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.DISPLAYORDER is '显示顺序'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.ORGDESCRIPTION is '机构描述'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.CATEGORY_ID is '机构类型'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.PARENT is '上级机构'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.PARENTS is '所有上级机构'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.ORGSHORTNAME is '简称'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.ORGHEADHUMACODE is '部门主管账号'
/

comment on column SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION.ORGHEADHUMANAME is '部门主管姓名'
/

create table SYT_NEW_PORTAL.SYT_SYS_ORGANIZATION_USER
(
    ORGANIZATION_ID VARCHAR2(32 char),
    USER_ID         VARCHAR2(32 char)
)
    /

create table SYT_NEW_PORTAL.SYT_TODOLIST
(
    ID         VARCHAR2(255 char) not null
        constraint SYS_C0018314
        check ("ID" IS NOT NULL),
    CATEGORY   VARCHAR2(255 char),
    CONTENT    VARCHAR2(255 char),
    CREATETIME TIMESTAMP(6),
    TYPE       VARCHAR2(255 char),
    USERNAME   VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_USER_DATA
(
    ID           VARCHAR2(32) not null
        constraint SYS_C0018315
        check ("ID" IS NOT NULL),
    TITLE        VARCHAR2(255),
    CONTENT      VARCHAR2(255),
    DETAIL_LINKS VARCHAR2(255),
    CATEGORY_ID  VARCHAR2(255),
    CREATE_TIME  DATE,
    USER_NAME    VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYS_RESOURCE_LINK
(
    ID        VARCHAR2(32) not null
        primary key,
    PATTERN   VARCHAR2(255),
    NAME      VARCHAR2(255),
    TYPE      VARCHAR2(255),
    PARENT_ID VARCHAR2(32)
)
    /

create table SYT_NEW_PORTAL.SYS_ROLE_RESOURCE_LINK
(
    ROLE_ID     VARCHAR2(32) not null,
    RESOURCE_ID VARCHAR2(32)
)
    /

create table SYT_NEW_PORTAL.SYS_USER_ROLE
(
    USER_ID VARCHAR2(32) not null,
    ROLE_ID VARCHAR2(32) not null
)
    /

create table SYT_NEW_PORTAL.SYS_LOGIN_LOGS
(
    ID          VARCHAR2(32) not null
        primary key,
    CLIENT_ID   VARCHAR2(32),
    HUMAN_CODE  VARCHAR2(32),
    IP          VARCHAR2(32),
    LOGIN_TIME  TIMESTAMP(6),
    LOGOUT_TIME TIMESTAMP(6),
    USER_AGENT  VARCHAR2(150),
    ADDRESS     VARCHAR2(128)
)
    /

create table SYT_NEW_PORTAL.SYS_RESOURCE
(
    ID          VARCHAR2(32) not null,
    LABEL       VARCHAR2(100) default NULL,
    IS_BTN      VARCHAR2(10)  default '否',
    ICON        VARCHAR2(255) default NULL,
    PARAM       VARCHAR2(255) default NULL,
    PATH        VARCHAR2(255) default NULL,
    PARENT_ID   VARCHAR2(32)  default NULL,
    STATUS      VARCHAR2(10)  default NULL,
    DESCRIPTION VARCHAR2(255) default NULL,
    URL         VARCHAR2(255) default NULL,
    COMPONENT   VARCHAR2(255) default NULL,
    TYPE        NUMBER(10),
    SORT        NUMBER(10),
    NAME        VARCHAR2(255 char),
    REDIRECT    VARCHAR2(255 char),
    TITLE       VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYS_ROLE_RESOURCE
(
    ROLE_ID     VARCHAR2(32 char) not null,
    RESOURCE_ID VARCHAR2(32 char) not null
)
    /

create table SYT_NEW_PORTAL.SYS_ROLE
(
    ID        VARCHAR2(32) not null
        constraint ID
        primary key,
    ROLE_NAME VARCHAR2(32),
    ROLE_KEY  VARCHAR2(32),
    REMARK    VARCHAR2(128)
)
    /

create table SYT_NEW_PORTAL.SYT_JUST_AUTH_USER
(
    ID         VARCHAR2(50) not null
        primary key,
    USERID     VARCHAR2(255),
    HUMANCODE  VARCHAR2(255),
    USERNAME   VARCHAR2(255),
    CREATEDATE DATE,
    TOKEN      VARCHAR2(255),
    EMAIL      VARCHAR2(255),
    MOBILE     VARCHAR2(255),
    AUTHTYPE   VARCHAR2(255),
    SFBIND     VARCHAR2(255),
    MODIFYDATE DATE,
    AVATAR     VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.SYT_JUST_AUTH_CONFIG
(
    ID              VARCHAR2(50) not null
        primary key,
    AUTHTYPE        VARCHAR2(255),
    CLIENTID        VARCHAR2(255),
    CLIENTSECRET    VARCHAR2(255),
    REDIRECTURI     VARCHAR2(255),
    ALIPAYPUBLICKEY VARCHAR2(255),
    AGENTID         VARCHAR2(255),
    CREATEDATE      DATE,
    MODIFYDATE      DATE,
    BZ              VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.SYT_DESKTOP_APP
(
    ID                 VARCHAR2(255),
    NAME               VARCHAR2(255),
    REMARK             VARCHAR2(255),
    DESKTOP_ID         VARCHAR2(255),
    SORT               NUMBER,
    STATUS             VARCHAR2(255),
    ICON               VARCHAR2(255),
    ROLE_ID            VARCHAR2(4000),
    ORG_ID             VARCHAR2(4000),
    ROUTER_LINK        VARCHAR2(255),
    COMPONENT          VARCHAR2(255),
    COMPONENTAPP       VARCHAR2(255 char),
    IMG                VARCHAR2(255 char),
    SYT_DESKTOP_APP    VARCHAR2(255),
    OPEN_TYPE          VARCHAR2(255 char),
    VISITOR_VISIBILITY VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_DESKTOP_APP is '桌面关联APP'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.REMARK is '描述'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.DESKTOP_ID is '桌面ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.STATUS is '状态'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.ICON is '图标'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.ROLE_ID is '所属角色多个逗号分隔'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.ORG_ID is '所属组织机构, 部门'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.ROUTER_LINK is '路由地址'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.COMPONENT is '移动端地址'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.SYT_DESKTOP_APP is '打开方式'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_APP.VISITOR_VISIBILITY is '游客可见'
/

create table SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT
(
    ID                VARCHAR2(255),
    ACCOUNT_ID        VARCHAR2(255),
    SERVICE_CENTER_ID VARCHAR2(255),
    COUNT             NUMBER,
    CREATE_DATE       TIMESTAMP(6),
    MODIFY_DATE       TIMESTAMP(6)
)
    /

comment on table SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT is '服务中心访问记录'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT.ACCOUNT_ID is '用户id'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT.SERVICE_CENTER_ID is '服务ID'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT.COUNT is '访问次数'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT.CREATE_DATE is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_SERVICE_CENTER_VISIT.MODIFY_DATE is '修改时间'
/

create table SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_USER
(
    ID           VARCHAR2(255),
    ACCOUNT_ID   VARCHAR2(255),
    DESKTOP_ID   VARCHAR2(255),
    BLOCK_ID     VARCHAR2(255),
    FRONT_CONFIG VARCHAR2(4000),
    CREATE_TIME  TIMESTAMP(6),
    ROLE_ID      VARCHAR2(255 char),
    HEIGHT       NUMBER(10)
)
    /

comment on table SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_USER is '用户板块关联表'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_USER.ACCOUNT_ID is '用户账户'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_USER.DESKTOP_ID is '桌面ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_USER.BLOCK_ID is '板块ID'
/

comment on column SYT_NEW_PORTAL.SYT_DESKTOP_BLOCK_USER.FRONT_CONFIG is '前端组件配置'
/

create table SYT_NEW_PORTAL.BPM_FORM
(
    ID            VARCHAR2(50) not null
        constraint BPM_FORM_PK
        primary key,
    NAME          VARCHAR2(255),
    STATUS        NUMBER,
    REMARK        VARCHAR2(1000),
    CONF          VARCHAR2(2000),
    FIELDS        CLOB,
    MOBILEFIELDS  CLOB,
    FIELDS_VALUES CLOB,
    FORM_FIELDS   CLOB
)
    /

comment on table SYT_NEW_PORTAL.BPM_FORM is '工作流表单'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.NAME is '表单名'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.STATUS is '状态'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.REMARK is '备注'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.CONF is '表单配置'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.FIELDS is '表单项'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.MOBILEFIELDS is '移动端字段'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.FIELDS_VALUES is '字段值'
/

comment on column SYT_NEW_PORTAL.BPM_FORM.FORM_FIELDS is '表单字段，保存表单的字段，以及字段的中文名称，用于后面列表页数据显示、自定义列显示（[{field_EN: "",field_ZH: '''',}]）'
/

create table SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT
(
    ID                   VARCHAR2(255),
    PROCESSDEFINITIONID  VARCHAR2(255),
    MODELID              VARCHAR2(255),
    DESCRIPTION          VARCHAR2(2000),
    FORMTYPE             VARCHAR2(255),
    FORMID               VARCHAR2(255),
    FORMCUSTOMCREATEPATH VARCHAR2(255),
    FORMCUSTOMVIEWPATH   VARCHAR2(255),
    CREATETIME           TIMESTAMP(6),
    CREATOR              VARCHAR2(255),
    UPDATER              VARCHAR2(255),
    FORMFIELDS           CLOB,
    FORMCONF             CLOB,
    CONDITIONIDS         VARCHAR2(255),
    MOBILEFIELDS         CLOB,
    FIELDS_VALUES        CLOB
)
    /

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.PROCESSDEFINITIONID is '流程定义的编号，关联 ProcessDefinition 的 id 属性'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.MODELID is '流程模型的编号，关联 Model 的 id 属性'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.DESCRIPTION is '描述'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FORMTYPE is '表单类型，关联BpmModelFormTypeEnum'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FORMID is '动态表单编号'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FORMCUSTOMCREATEPATH is '自定义表单的提交路径，使用 Vue 的路由地址'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FORMCUSTOMVIEWPATH is '自定义表单的查看路径，使用 Vue 的路由地址'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.CREATETIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.CREATOR is '创建者'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.UPDATER is '更新者'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FORMFIELDS is '表单项的数组'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FORMCONF is '表单的配置'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.CONDITIONIDS is '流转条件表达式相关的字段，英文逗号拼接的'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.MOBILEFIELDS is '移动端字段'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_DEFINITION_EXT.FIELDS_VALUES is '字段值'
/

create table SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE
(
    ID                  VARCHAR2(50),
    MODELID             VARCHAR2(50),
    PROCESSDEFINITIONID VARCHAR2(50),
    TASKDEFINITIONKEY   VARCHAR2(255),
    TYPE                NUMBER,
    OPTIONS             VARCHAR2(4000)
)
    /

comment on table SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE is '任务分配的规则表'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE.MODELID is '流程模型编号'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE.PROCESSDEFINITIONID is '流程定义编号'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE.TASKDEFINITIONKEY is '流程任务的定义 Key'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE.TYPE is '规则类型'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_ASSIGN_RULE.OPTIONS is '规则值数组，一般关联指定表的编号'
/

create table SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT
(
    ID                  VARCHAR2(50) not null
        primary key,
    STARTUSERID         VARCHAR2(255),
    NAME                VARCHAR2(255),
    PROCESSINSTANCEID   VARCHAR2(255),
    FORMVARIABLES       CLOB,
    PROCESSDEFINITIONID VARCHAR2(255),
    CATEGORY            VARCHAR2(255),
    STATUS              NUMBER,
    RESULT              NUMBER,
    ENDTIME             TIMESTAMP(6),
    CREATETIME          TIMESTAMP(6),
    CREATOR             VARCHAR2(255),
    UPDATER             VARCHAR2(255),
    UPDATETIME          TIMESTAMP(6),
    STARTUSERNAME       VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT is '工作流表单'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.STARTUSERID is '发起流程的用户编号，冗余 HistoricProcessInstance 的 startUserId 属性'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.NAME is '流程实例的名字，冗余 ProcessInstance 的 name 属性，用于筛选'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.PROCESSINSTANCEID is '流程实例的编号，关联 ProcessInstance 的 id 属性'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.FORMVARIABLES is '提交的表单值'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.PROCESSDEFINITIONID is '流程定义的编号，关联 ProcessDefinition 的 id 属性'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.CATEGORY is '流程分类，冗余 ProcessDefinition 的 category 属性，数据字典 bpm_model_category'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.STATUS is '流程实例的状态'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.RESULT is '流程实例的结果'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.ENDTIME is '结束时间，冗余 HistoricProcessInstance 的 endTime 属性'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.CREATETIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.CREATOR is '创建者'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.UPDATER is '更新者'
/

comment on column SYT_NEW_PORTAL.BPM_PROCESS_INSTANCE_EXT.UPDATETIME is '最后更新时间'
/

create table SYT_NEW_PORTAL.BPM_TASK_EXT
(
    ID                       VARCHAR2(50) not null,
    ASSIGNEEUSERID           VARCHAR2(255),
    NAME                     VARCHAR2(255),
    TASKID                   VARCHAR2(255),
    RESULT                   NUMBER,
    REASON                   VARCHAR2(2000),
    ENDTIME                  TIMESTAMP(6),
    PROCESSINSTANCEID        VARCHAR2(255),
    PROCESSDEFINITIONID      VARCHAR2(255),
    CREATETIME               TIMESTAMP(6),
    EXTENSIONELEMENTS        CLOB,
    ATTRIBUTES               CLOB,
    FIELDS_PERMISSION_JSON   CLOB,
    COPY_LIST_JSON           CLOB,
    FORM_VARIABLES           CLOB,
    BUTTONS_JSON             CLOB,
    EXTENDED_PROPERTIES_JSON CLOB,
    RETURN_TASK_DEF_KEY      VARCHAR2(255),
    DEL_ACTIVITY_IDS         VARCHAR2(2000),
    TASKDEFKEY               VARCHAR2(255),
    TASKDATATYPE             VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.BPM_TASK_EXT is '流程任务的拓展表'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.NAME is '任务的名字'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.TASKID is '任务的编号'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.RESULT is '任务的结果'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.REASON is '审批建议'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.ENDTIME is '任务的结束时间'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.PROCESSINSTANCEID is '流程实例的编号'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.PROCESSDEFINITIONID is '流程定义的编号'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.CREATETIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.EXTENSIONELEMENTS is '扩展元素(监听事件、自定义表单字段等)'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.ATTRIBUTES is '节点属性(审批按钮等)'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.FIELDS_PERMISSION_JSON is '表单字段权限'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.COPY_LIST_JSON is '抄送人'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.FORM_VARIABLES is '审批环节表单变量'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.BUTTONS_JSON is '审批按钮'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.EXTENDED_PROPERTIES_JSON is '扩展属性，比如控制每个节点的审批意见是否必填'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.RETURN_TASK_DEF_KEY is '驳回操作节点的任务标识；此字段有值，表示有驳回操作到该任务节点'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.DEL_ACTIVITY_IDS is '如果驳回后，流程走向改变了，删除跳转驳回时保留的记录'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.TASKDEFKEY is '任务定义key(节点的key)'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_EXT.TASKDATATYPE is 'flowable:dataType 用于查询节点类型(例如获取发起人节点)'
/

create index SYT_NEW_PORTAL.BPM_IDX_TASK_EXT_PROCINST
    on SYT_NEW_PORTAL.BPM_TASK_EXT (PROCESSINSTANCEID)
    /

create table SYT_NEW_PORTAL.BPM_CATEGORY
(
    ID     VARCHAR2(50),
    NAME   VARCHAR2(255),
    SORT   NUMBER,
    PARENT VARCHAR2(50)
)
    /

comment on table SYT_NEW_PORTAL.BPM_CATEGORY is '工作流分类，表单分类，模型分类'
/

comment on column SYT_NEW_PORTAL.BPM_CATEGORY.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.BPM_CATEGORY.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.BPM_CATEGORY.PARENT is '父节点'
/

create table SYT_NEW_PORTAL.BPM_MODEL_EXT
(
    ID          VARCHAR2(255),
    MODELID     VARCHAR2(255),
    TYPE        VARCHAR2(255),
    ORGID       CLOB,
    FUNCTIONBTN CLOB,
    ROLEID      CLOB
)
    /

comment on table SYT_NEW_PORTAL.BPM_MODEL_EXT is '流程模型扩展'
/

comment on column SYT_NEW_PORTAL.BPM_MODEL_EXT.TYPE is '类型,区分公共、私有流程'
/

comment on column SYT_NEW_PORTAL.BPM_MODEL_EXT.ORGID is '组织机构'
/

comment on column SYT_NEW_PORTAL.BPM_MODEL_EXT.FUNCTIONBTN is '可操作按钮'
/

comment on column SYT_NEW_PORTAL.BPM_MODEL_EXT.ROLEID is '角色'
/

create table SYT_NEW_PORTAL.BPM_TASK_BUTTON
(
    ID      VARCHAR2(50),
    KEY     VARCHAR2(255),
    NAME    VARCHAR2(255),
    STATUS  VARCHAR2(50),
    SORT    NUMBER,
    CLICK   VARCHAR2(255 char),
    ICON    VARCHAR2(255 char),
    TYPE    VARCHAR2(255 char),
    BTNSIZE VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.BPM_TASK_BUTTON is '流程按钮'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_BUTTON.KEY is '按钮标识'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_BUTTON.NAME is '按钮名称'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_BUTTON.STATUS is '状态'
/

comment on column SYT_NEW_PORTAL.BPM_TASK_BUTTON.SORT is '排序'
/

create table SYT_NEW_PORTAL.BPM_TASK_BUTTON_BAK
(
    ID      VARCHAR2(50),
    KEY     VARCHAR2(255),
    NAME    VARCHAR2(255),
    STATUS  VARCHAR2(50),
    SORT    NUMBER,
    CLICK   VARCHAR2(255 char),
    ICON    VARCHAR2(255 char),
    TYPE    VARCHAR2(255 char),
    BTNSIZE VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.BPM_FLOW_LISTENER
(
    ID           VARCHAR2(50),
    TYPE         VARCHAR2(255),
    NAME         VARCHAR2(255),
    LISTENERTYPE VARCHAR2(255),
    VALUE        VARCHAR2(255),
    REMARK       VARCHAR2(1000),
    EVENTTYPE    VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.BPM_FLOW_LISTENER is '流程监听器'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER.TYPE is '类型(java 表达式 代理表达式)'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER.LISTENERTYPE is '监听器类型'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER.VALUE is '值'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER.REMARK is '备注'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER.EVENTTYPE is '事件类型'
/

create table SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM
(
    ID         VARCHAR2(50),
    LISTENERID VARCHAR2(50),
    NAME       VARCHAR2(255),
    VALUE      VARCHAR2(255),
    PARAMTYPE  VARCHAR2(255),
    REQUIRED   VARCHAR2(50)
)
    /

comment on table SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM is '监听器参数'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM.LISTENERID is '监听器id'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM.VALUE is '值'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM.PARAMTYPE is '监听器参数类型-0.字符串 1.表达式'
/

comment on column SYT_NEW_PORTAL.BPM_FLOW_LISTENER_PARAM.REQUIRED is '是否必填'
/

create table SYT_NEW_PORTAL.SYT_MESSAGE
(
    ID                         VARCHAR2(50),
    TITLE                      VARCHAR2(2000),
    CONTENT                    VARCHAR2(4000),
    TYPE                       VARCHAR2(255),
    CREATEDATE                 TIMESTAMP(6),
    CREATEUSER                 VARCHAR2(255),
    PROCESS_DEFINITION_ID      VARCHAR2(255 char),
    PROCESS_DEFINITION_KEY     VARCHAR2(255 char),
    PROCESS_DEFINITION_NAME    VARCHAR2(255 char),
    PROCESS_INSTANCE_ID        VARCHAR2(255 char),
    PROCESS_INSTANCE_INITIATOR VARCHAR2(255 char),
    REMIND_COUNT               NUMBER(10),
    TASK_ASSIGNEE              VARCHAR2(255 char),
    TASK_DEFINITION_KEY        VARCHAR2(255 char),
    TASK_FINISHED              NUMBER(1),
    TASK_ID                    VARCHAR2(255 char),
    TASK_NAME                  VARCHAR2(255 char),
    TASK_START_TIME            TIMESTAMP(6),
    USERIDS                    CLOB,
    ORGIDS                     CLOB,
    ROLEIDS                    CLOB
)
    /

comment on column SYT_NEW_PORTAL.SYT_MESSAGE.TITLE is '标题'
/

comment on column SYT_NEW_PORTAL.SYT_MESSAGE.CONTENT is '内容'
/

comment on column SYT_NEW_PORTAL.SYT_MESSAGE.TYPE is '类型'
/

create table SYT_NEW_PORTAL.SYT_MESSAGE_SEND
(
    ID                         VARCHAR2(50),
    MESSAGEID                  VARCHAR2(50),
    USERID                     VARCHAR2(50),
    STATUS                     NUMBER,
    SENDTIME                   TIMESTAMP(6),
    READTIME                   TIMESTAMP(6),
    CONTENT                    VARCHAR2(255 char),
    TITLE                      VARCHAR2(4000 char),
    TYPE                       VARCHAR2(255 char),
    USERNAME                   VARCHAR2(255 char),
    PROCESS_DEFINITION_ID      VARCHAR2(255 char),
    PROCESS_DEFINITION_KEY     VARCHAR2(255 char),
    PROCESS_DEFINITION_NAME    VARCHAR2(255 char),
    PROCESS_INSTANCE_ID        VARCHAR2(255 char),
    PROCESS_INSTANCE_INITIATOR VARCHAR2(255 char),
    REMIND_COUNT               NUMBER(10),
    TASK_ASSIGNEE              VARCHAR2(255 char),
    TASK_DEFINITION_KEY        VARCHAR2(255 char),
    TASK_FINISHED              NUMBER(1),
    TASK_ID                    VARCHAR2(255 char),
    TASK_NAME                  VARCHAR2(255 char),
    TASK_START_TIME            TIMESTAMP(6)
)
    /

comment on column SYT_NEW_PORTAL.SYT_MESSAGE_SEND.MESSAGEID is '关联消息id'
/

comment on column SYT_NEW_PORTAL.SYT_MESSAGE_SEND.USERID is '关联用户id'
/

comment on column SYT_NEW_PORTAL.SYT_MESSAGE_SEND.STATUS is '状态 0默认未读 1已读'
/

create table SYT_NEW_PORTAL.ACT_GE_PROPERTY
(
    NAME_  NVARCHAR2(64) not null
        primary key,
    VALUE_ NVARCHAR2(300),
    REV_   NUMBER
)
    /

create table SYT_NEW_PORTAL.FLW_EV_DATABASECHANGELOGLOCK
(
    ID          NUMBER    not null
        constraint PK_FLW_EV_DATABASECHANGELOGLOC
            primary key,
    LOCKED      NUMBER(1) not null,
    LOCKGRANTED TIMESTAMP(6),
    LOCKEDBY    VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.FLW_EV_DATABASECHANGELOG
(
    ID            VARCHAR2(255) not null,
    AUTHOR        VARCHAR2(255) not null,
    FILENAME      VARCHAR2(255) not null,
    DATEEXECUTED  TIMESTAMP(6)  not null,
    ORDEREXECUTED NUMBER        not null,
    EXECTYPE      VARCHAR2(10)  not null,
    MD5SUM        VARCHAR2(35),
    DESCRIPTION   VARCHAR2(255),
    COMMENTS      VARCHAR2(255),
    TAG           VARCHAR2(255),
    LIQUIBASE     VARCHAR2(20),
    CONTEXTS      VARCHAR2(255),
    LABELS        VARCHAR2(255),
    DEPLOYMENT_ID VARCHAR2(10)
)
    /

create table SYT_NEW_PORTAL.FLW_EVENT_DEPLOYMENT
(
    ID_                   VARCHAR2(255) not null
        constraint PK_FLW_EVENT_DEPLOYMENT
        primary key,
    NAME_                 VARCHAR2(255),
    CATEGORY_             VARCHAR2(255),
    DEPLOY_TIME_          TIMESTAMP(3),
    TENANT_ID_            VARCHAR2(255),
    PARENT_DEPLOYMENT_ID_ VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.FLW_EVENT_RESOURCE
(
    ID_             VARCHAR2(255) not null
        constraint PK_FLW_EVENT_RESOURCE
        primary key,
    NAME_           VARCHAR2(255),
    DEPLOYMENT_ID_  VARCHAR2(255),
    RESOURCE_BYTES_ BLOB
)
    /

create table SYT_NEW_PORTAL.FLW_EVENT_DEFINITION
(
    ID_            VARCHAR2(255) not null
        constraint PK_FLW_EVENT_DEFINITION
        primary key,
    NAME_          VARCHAR2(255),
    VERSION_       NUMBER,
    KEY_           VARCHAR2(255),
    CATEGORY_      VARCHAR2(255),
    DEPLOYMENT_ID_ VARCHAR2(255),
    TENANT_ID_     VARCHAR2(255),
    RESOURCE_NAME_ VARCHAR2(255),
    DESCRIPTION_   VARCHAR2(255)
)
    /

create unique index SYT_NEW_PORTAL.ACT_IDX_EVENT_DEF_UNIQ
    on SYT_NEW_PORTAL.FLW_EVENT_DEFINITION (KEY_, VERSION_, TENANT_ID_)
    /

create table SYT_NEW_PORTAL.FLW_CHANNEL_DEFINITION
(
    ID_            VARCHAR2(255) not null
        constraint PK_FLW_CHANNEL_DEFINITION
        primary key,
    NAME_          VARCHAR2(255),
    VERSION_       NUMBER,
    KEY_           VARCHAR2(255),
    CATEGORY_      VARCHAR2(255),
    DEPLOYMENT_ID_ VARCHAR2(255),
    CREATE_TIME_   TIMESTAMP(3),
    TENANT_ID_     VARCHAR2(255),
    RESOURCE_NAME_ VARCHAR2(255),
    DESCRIPTION_   VARCHAR2(255)
)
    /

create unique index SYT_NEW_PORTAL.ACT_IDX_CHANNEL_DEF_UNIQ
    on SYT_NEW_PORTAL.FLW_CHANNEL_DEFINITION (KEY_, VERSION_, TENANT_ID_)
    /

create table SYT_NEW_PORTAL.ACT_ID_PROPERTY
(
    NAME_  NVARCHAR2(64) not null
        primary key,
    VALUE_ NVARCHAR2(300),
    REV_   NUMBER
)
    /

create table SYT_NEW_PORTAL.ACT_ID_BYTEARRAY
(
    ID_    NVARCHAR2(64) not null
        primary key,
    REV_   NUMBER,
    NAME_  NVARCHAR2(255),
    BYTES_ BLOB
)
    /

create table SYT_NEW_PORTAL.ACT_ID_GROUP
(
    ID_   NVARCHAR2(64) not null
        primary key,
    REV_  NUMBER,
    NAME_ NVARCHAR2(255),
    TYPE_ NVARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.ACT_ID_USER
(
    ID_           NVARCHAR2(64) not null
        primary key,
    REV_          NUMBER,
    FIRST_        NVARCHAR2(255),
    LAST_         NVARCHAR2(255),
    DISPLAY_NAME_ NVARCHAR2(255),
    EMAIL_        NVARCHAR2(255),
    PWD_          NVARCHAR2(255),
    PICTURE_ID_   NVARCHAR2(64),
    TENANT_ID_    NVARCHAR2(255) default ''
)
    /

create table SYT_NEW_PORTAL.ACT_ID_MEMBERSHIP
(
    USER_ID_  NVARCHAR2(64) not null
        constraint ACT_FK_MEMB_USER
        references SYT_NEW_PORTAL.ACT_ID_USER,
    GROUP_ID_ NVARCHAR2(64) not null
        constraint ACT_FK_MEMB_GROUP
        references SYT_NEW_PORTAL.ACT_ID_GROUP,
    primary key (USER_ID_, GROUP_ID_)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_MEMB_GROUP
    on SYT_NEW_PORTAL.ACT_ID_MEMBERSHIP (GROUP_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_MEMB_USER
    on SYT_NEW_PORTAL.ACT_ID_MEMBERSHIP (USER_ID_)
    /

create table SYT_NEW_PORTAL.ACT_ID_INFO
(
    ID_        NVARCHAR2(64) not null
        primary key,
    REV_       NUMBER,
    USER_ID_   NVARCHAR2(64),
    TYPE_      NVARCHAR2(64),
    KEY_       NVARCHAR2(255),
    VALUE_     NVARCHAR2(255),
    PASSWORD_  BLOB,
    PARENT_ID_ NVARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.ACT_ID_TOKEN
(
    ID_          NVARCHAR2(64) not null
        primary key,
    REV_         NUMBER,
    TOKEN_VALUE_ NVARCHAR2(255),
    TOKEN_DATE_  TIMESTAMP(6),
    IP_ADDRESS_  NVARCHAR2(255),
    USER_AGENT_  NVARCHAR2(255),
    USER_ID_     NVARCHAR2(255),
    TOKEN_DATA_  NVARCHAR2(2000)
)
    /

create table SYT_NEW_PORTAL.ACT_ID_PRIV
(
    ID_   NVARCHAR2(64)  not null
        primary key,
    NAME_ NVARCHAR2(255) not null
        constraint ACT_UNIQ_PRIV_NAME
        unique
)
    /

create table SYT_NEW_PORTAL.ACT_ID_PRIV_MAPPING
(
    ID_       NVARCHAR2(64) not null
        primary key,
    PRIV_ID_  NVARCHAR2(64) not null
        constraint ACT_FK_PRIV_MAPPING
        references SYT_NEW_PORTAL.ACT_ID_PRIV,
    USER_ID_  NVARCHAR2(255),
    GROUP_ID_ NVARCHAR2(255)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_PRIV_MAPPING
    on SYT_NEW_PORTAL.ACT_ID_PRIV_MAPPING (PRIV_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_PRIV_USER
    on SYT_NEW_PORTAL.ACT_ID_PRIV_MAPPING (USER_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_PRIV_GROUP
    on SYT_NEW_PORTAL.ACT_ID_PRIV_MAPPING (GROUP_ID_)
    /

create table SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK
(
    ID_                  NVARCHAR2(64) not null
        primary key,
    GROUP_ID_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255),
    USER_ID_             NVARCHAR2(255),
    TASK_ID_             NVARCHAR2(64),
    CREATE_TIME_         TIMESTAMP(6),
    PROC_INST_ID_        NVARCHAR2(64),
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_IDENT_LNK_USER
    on SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK (USER_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_IDENT_LNK_SCOPE
    on SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_IDENT_LNK_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_IDENT_LNK_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_IDENT_LNK_TASK
    on SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK (TASK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_IDENT_LNK_PROCINST
    on SYT_NEW_PORTAL.ACT_HI_IDENTITYLINK (PROC_INST_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_ENTITYLINK
(
    ID_                      NVARCHAR2(64) not null
        primary key,
    REV_                     NUMBER,
    CREATE_TIME_             TIMESTAMP(6),
    LINK_TYPE_               NVARCHAR2(255),
    SCOPE_ID_                NVARCHAR2(255),
    SUB_SCOPE_ID_            NVARCHAR2(255),
    SCOPE_TYPE_              NVARCHAR2(255),
    SCOPE_DEFINITION_ID_     NVARCHAR2(255),
    PARENT_ELEMENT_ID_       NVARCHAR2(255),
    REF_SCOPE_ID_            NVARCHAR2(255),
    REF_SCOPE_TYPE_          NVARCHAR2(255),
    REF_SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    ROOT_SCOPE_ID_           NVARCHAR2(255),
    ROOT_SCOPE_TYPE_         NVARCHAR2(255),
    HIERARCHY_TYPE_          NVARCHAR2(255)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_ENT_LNK_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_ENTITYLINK (SCOPE_ID_, SCOPE_TYPE_, LINK_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_ENT_LNK_ROOT_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_ENTITYLINK (ROOT_SCOPE_ID_, ROOT_SCOPE_TYPE_, LINK_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_ENT_LNK_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_ENTITYLINK (SCOPE_DEFINITION_ID_, SCOPE_TYPE_, LINK_TYPE_)
    /

create table SYT_NEW_PORTAL.ACT_HI_ENTITYLINK
(
    ID_                      NVARCHAR2(64) not null
        primary key,
    LINK_TYPE_               NVARCHAR2(255),
    CREATE_TIME_             TIMESTAMP(6),
    SCOPE_ID_                NVARCHAR2(255),
    SUB_SCOPE_ID_            NVARCHAR2(255),
    SCOPE_TYPE_              NVARCHAR2(255),
    SCOPE_DEFINITION_ID_     NVARCHAR2(255),
    PARENT_ELEMENT_ID_       NVARCHAR2(255),
    REF_SCOPE_ID_            NVARCHAR2(255),
    REF_SCOPE_TYPE_          NVARCHAR2(255),
    REF_SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    ROOT_SCOPE_ID_           NVARCHAR2(255),
    ROOT_SCOPE_TYPE_         NVARCHAR2(255),
    HIERARCHY_TYPE_          NVARCHAR2(255)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ENT_LNK_SCOPE
    on SYT_NEW_PORTAL.ACT_HI_ENTITYLINK (SCOPE_ID_, SCOPE_TYPE_, LINK_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ENT_LNK_ROOT_SCOPE
    on SYT_NEW_PORTAL.ACT_HI_ENTITYLINK (ROOT_SCOPE_ID_, ROOT_SCOPE_TYPE_, LINK_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ENT_LNK_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_HI_ENTITYLINK (SCOPE_DEFINITION_ID_, SCOPE_TYPE_, LINK_TYPE_)
    /

create table SYT_NEW_PORTAL.ACT_HI_TASKINST
(
    ID_                       NVARCHAR2(64) not null
        primary key,
    REV_                      NUMBER         default 1,
    PROC_DEF_ID_              NVARCHAR2(64),
    TASK_DEF_ID_              NVARCHAR2(64),
    TASK_DEF_KEY_             NVARCHAR2(255),
    PROC_INST_ID_             NVARCHAR2(64),
    EXECUTION_ID_             NVARCHAR2(64),
    SCOPE_ID_                 NVARCHAR2(255),
    SUB_SCOPE_ID_             NVARCHAR2(255),
    SCOPE_TYPE_               NVARCHAR2(255),
    SCOPE_DEFINITION_ID_      NVARCHAR2(255),
    PROPAGATED_STAGE_INST_ID_ NVARCHAR2(255),
    PARENT_TASK_ID_           NVARCHAR2(64),
    NAME_                     NVARCHAR2(255),
    DESCRIPTION_              NVARCHAR2(2000),
    OWNER_                    NVARCHAR2(255),
    ASSIGNEE_                 NVARCHAR2(255),
    START_TIME_               TIMESTAMP(6)  not null,
    CLAIM_TIME_               TIMESTAMP(6),
    END_TIME_                 TIMESTAMP(6),
    DURATION_                 NUMBER(19),
    DELETE_REASON_            NVARCHAR2(2000),
    PRIORITY_                 NUMBER,
    DUE_DATE_                 TIMESTAMP(6),
    FORM_KEY_                 NVARCHAR2(255),
    CATEGORY_                 NVARCHAR2(255),
    TENANT_ID_                NVARCHAR2(255) default '',
    LAST_UPDATED_TIME_        TIMESTAMP(6)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_TASK_SCOPE
    on SYT_NEW_PORTAL.ACT_HI_TASKINST (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_TASK_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_HI_TASKINST (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_TASK_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_HI_TASKINST (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_TASK_INST_PROCINST
    on SYT_NEW_PORTAL.ACT_HI_TASKINST (PROC_INST_ID_)
    /

create table SYT_NEW_PORTAL.ACT_HI_TSK_LOG
(
    ID_                  NUMBER(19)    not null
        primary key,
    TYPE_                NVARCHAR2(64),
    TASK_ID_             NVARCHAR2(64) not null,
    TIME_STAMP_          TIMESTAMP(6)  not null,
    USER_ID_             NVARCHAR2(255),
    DATA_                NVARCHAR2(2000),
    EXECUTION_ID_        NVARCHAR2(64),
    PROC_INST_ID_        NVARCHAR2(64),
    PROC_DEF_ID_         NVARCHAR2(64),
    SCOPE_ID_            NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create table SYT_NEW_PORTAL.ACT_HI_VARINST
(
    ID_                NVARCHAR2(64)  not null
        primary key,
    REV_               NUMBER default 1,
    PROC_INST_ID_      NVARCHAR2(64),
    EXECUTION_ID_      NVARCHAR2(64),
    TASK_ID_           NVARCHAR2(64),
    NAME_              NVARCHAR2(255) not null,
    VAR_TYPE_          NVARCHAR2(100),
    SCOPE_ID_          NVARCHAR2(255),
    SUB_SCOPE_ID_      NVARCHAR2(255),
    SCOPE_TYPE_        NVARCHAR2(255),
    BYTEARRAY_ID_      NVARCHAR2(64),
    DOUBLE_            NUMBER,
    LONG_              NUMBER(19),
    TEXT_              NVARCHAR2(2000),
    TEXT2_             NVARCHAR2(2000),
    CREATE_TIME_       TIMESTAMP(6),
    LAST_UPDATED_TIME_ TIMESTAMP(6)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_PROCVAR_NAME_TYPE
    on SYT_NEW_PORTAL.ACT_HI_VARINST (NAME_, VAR_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_VAR_SCOPE_ID_TYPE
    on SYT_NEW_PORTAL.ACT_HI_VARINST (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_VAR_SUB_ID_TYPE
    on SYT_NEW_PORTAL.ACT_HI_VARINST (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_PROCVAR_PROC_INST
    on SYT_NEW_PORTAL.ACT_HI_VARINST (PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_PROCVAR_TASK_ID
    on SYT_NEW_PORTAL.ACT_HI_VARINST (TASK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_PROCVAR_EXE
    on SYT_NEW_PORTAL.ACT_HI_VARINST (EXECUTION_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_HISTORY_JOB
(
    ID_                 NVARCHAR2(64) not null
        primary key,
    REV_                NUMBER,
    LOCK_EXP_TIME_      TIMESTAMP(6),
    LOCK_OWNER_         NVARCHAR2(255),
    RETRIES_            NUMBER,
    EXCEPTION_STACK_ID_ NVARCHAR2(64),
    EXCEPTION_MSG_      NVARCHAR2(2000),
    HANDLER_TYPE_       NVARCHAR2(255),
    HANDLER_CFG_        NVARCHAR2(2000),
    CUSTOM_VALUES_ID_   NVARCHAR2(64),
    ADV_HANDLER_CFG_ID_ NVARCHAR2(64),
    CREATE_TIME_        TIMESTAMP(6),
    SCOPE_TYPE_         NVARCHAR2(255),
    TENANT_ID_          NVARCHAR2(255) default ''
)
    /

create table SYT_NEW_PORTAL.FLW_RU_BATCH
(
    ID_            NVARCHAR2(64) not null
        primary key,
    REV_           NUMBER,
    TYPE_          NVARCHAR2(64) not null,
    SEARCH_KEY_    NVARCHAR2(255),
    SEARCH_KEY2_   NVARCHAR2(255),
    CREATE_TIME_   TIMESTAMP(6)  not null,
    COMPLETE_TIME_ TIMESTAMP(6),
    STATUS_        NVARCHAR2(255),
    BATCH_DOC_ID_  NVARCHAR2(64),
    TENANT_ID_     NVARCHAR2(255) default ''
)
    /

create table SYT_NEW_PORTAL.FLW_RU_BATCH_PART
(
    ID_            NVARCHAR2(64) not null
        primary key,
    REV_           NUMBER,
    BATCH_ID_      NVARCHAR2(64)
        constraint FLW_FK_BATCH_PART_PARENT
        references SYT_NEW_PORTAL.FLW_RU_BATCH,
    TYPE_          NVARCHAR2(64) not null,
    SCOPE_ID_      NVARCHAR2(64),
    SUB_SCOPE_ID_  NVARCHAR2(64),
    SCOPE_TYPE_    NVARCHAR2(64),
    SEARCH_KEY_    NVARCHAR2(255),
    SEARCH_KEY2_   NVARCHAR2(255),
    CREATE_TIME_   TIMESTAMP(6)  not null,
    COMPLETE_TIME_ TIMESTAMP(6),
    STATUS_        NVARCHAR2(255),
    RESULT_DOC_ID_ NVARCHAR2(64),
    TENANT_ID_     NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.FLW_IDX_BATCH_PART
    on SYT_NEW_PORTAL.FLW_RU_BATCH_PART (BATCH_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RE_DEPLOYMENT
(
    ID_                   NVARCHAR2(64) not null
        primary key,
    NAME_                 NVARCHAR2(255),
    CATEGORY_             NVARCHAR2(255),
    KEY_                  NVARCHAR2(255),
    TENANT_ID_            NVARCHAR2(255) default '',
    DEPLOY_TIME_          TIMESTAMP(6),
    DERIVED_FROM_         NVARCHAR2(64),
    DERIVED_FROM_ROOT_    NVARCHAR2(64),
    PARENT_DEPLOYMENT_ID_ NVARCHAR2(255),
    ENGINE_VERSION_       NVARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.ACT_GE_BYTEARRAY
(
    ID_            NVARCHAR2(64) not null
        primary key,
    REV_           NUMBER,
    NAME_          NVARCHAR2(255),
    DEPLOYMENT_ID_ NVARCHAR2(64)
        constraint ACT_FK_BYTEARR_DEPL
        references SYT_NEW_PORTAL.ACT_RE_DEPLOYMENT,
    BYTES_         BLOB,
    GENERATED_     NUMBER(1)
        check (GENERATED_ IN (1, 0))
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_BYTEAR_DEPL
    on SYT_NEW_PORTAL.ACT_GE_BYTEARRAY (DEPLOYMENT_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB
(
    ID_                  NVARCHAR2(64)  not null
        primary key,
    REV_                 NUMBER,
    CATEGORY_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255) not null,
    LOCK_EXP_TIME_       TIMESTAMP(6),
    LOCK_OWNER_          NVARCHAR2(255),
    EXCLUSIVE_           NUMBER(1)
        check (EXCLUSIVE_ IN (1, 0)),
    EXECUTION_ID_        NVARCHAR2(64),
    PROCESS_INSTANCE_ID_ NVARCHAR2(64),
    PROC_DEF_ID_         NVARCHAR2(64),
    ELEMENT_ID_          NVARCHAR2(255),
    ELEMENT_NAME_        NVARCHAR2(255),
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    CORRELATION_ID_      NVARCHAR2(255),
    RETRIES_             NUMBER,
    EXCEPTION_STACK_ID_  NVARCHAR2(64)
        constraint ACT_FK_EJOB_EXCEPTION
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    EXCEPTION_MSG_       NVARCHAR2(2000),
    DUEDATE_             TIMESTAMP(6),
    REPEAT_              NVARCHAR2(255),
    HANDLER_TYPE_        NVARCHAR2(255),
    HANDLER_CFG_         NVARCHAR2(2000),
    CUSTOM_VALUES_ID_    NVARCHAR2(64)
        constraint ACT_FK_EJOB_CUSTOM_VAL
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    CREATE_TIME_         TIMESTAMP(6),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EJOB_EXCEPTION
    on SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB (EXCEPTION_STACK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EJOB_CUSTOM_VAL_ID
    on SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB (CUSTOM_VALUES_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EJOB_CORRELATION_ID
    on SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB (CORRELATION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EJOB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EJOB_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EJOB_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_EXTERNAL_JOB (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create table SYT_NEW_PORTAL.ACT_RE_MODEL
(
    ID_                           NVARCHAR2(64) not null
        primary key,
    REV_                          NUMBER,
    NAME_                         NVARCHAR2(255),
    KEY_                          NVARCHAR2(255),
    CATEGORY_                     NVARCHAR2(255),
    CREATE_TIME_                  TIMESTAMP(6),
    LAST_UPDATE_TIME_             TIMESTAMP(6),
    VERSION_                      NUMBER,
    META_INFO_                    NVARCHAR2(2000),
    DEPLOYMENT_ID_                NVARCHAR2(64)
        constraint ACT_FK_MODEL_DEPLOYMENT
        references SYT_NEW_PORTAL.ACT_RE_DEPLOYMENT,
    EDITOR_SOURCE_VALUE_ID_       NVARCHAR2(64)
        constraint ACT_FK_MODEL_SOURCE
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    EDITOR_SOURCE_EXTRA_VALUE_ID_ NVARCHAR2(64)
        constraint ACT_FK_MODEL_SOURCE_EXTRA
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    TENANT_ID_                    NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_MODEL_SOURCE
    on SYT_NEW_PORTAL.ACT_RE_MODEL (EDITOR_SOURCE_VALUE_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_MODEL_SOURCE_EXTRA
    on SYT_NEW_PORTAL.ACT_RE_MODEL (EDITOR_SOURCE_EXTRA_VALUE_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_MODEL_DEPLOYMENT
    on SYT_NEW_PORTAL.ACT_RE_MODEL (DEPLOYMENT_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RE_PROCDEF
(
    ID_                     NVARCHAR2(64)            not null
        primary key,
    REV_                    NUMBER,
    CATEGORY_               NVARCHAR2(255),
    NAME_                   NVARCHAR2(255),
    KEY_                    NVARCHAR2(255)           not null,
    VERSION_                NUMBER                   not null,
    DEPLOYMENT_ID_          NVARCHAR2(64),
    RESOURCE_NAME_          NVARCHAR2(2000),
    DGRM_RESOURCE_NAME_     VARCHAR2(4000),
    DESCRIPTION_            NVARCHAR2(2000),
    HAS_START_FORM_KEY_     NUMBER(1)
        check (HAS_START_FORM_KEY_ IN (1, 0)),
    HAS_GRAPHICAL_NOTATION_ NUMBER(1)
        check (HAS_GRAPHICAL_NOTATION_ IN (1, 0)),
    SUSPENSION_STATE_       NUMBER,
    TENANT_ID_              NVARCHAR2(255) default '',
    DERIVED_FROM_           NVARCHAR2(64),
    DERIVED_FROM_ROOT_      NVARCHAR2(64),
    DERIVED_VERSION_        NUMBER         default 0 not null,
    ENGINE_VERSION_         NVARCHAR2(255),
    constraint ACT_UNIQ_PROCDEF
        unique (KEY_, VERSION_, DERIVED_VERSION_, TENANT_ID_)
)
    /

create table SYT_NEW_PORTAL.ACT_RU_EXECUTION
(
    ID_                        NVARCHAR2(64) not null
        primary key,
    REV_                       NUMBER,
    PROC_INST_ID_              NVARCHAR2(64)
        constraint ACT_FK_EXE_PROCINST
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    BUSINESS_KEY_              NVARCHAR2(255),
    PARENT_ID_                 NVARCHAR2(64)
        constraint ACT_FK_EXE_PARENT
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_               NVARCHAR2(64)
        constraint ACT_FK_EXE_PROCDEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    SUPER_EXEC_                NVARCHAR2(64)
        constraint ACT_FK_EXE_SUPER
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    ROOT_PROC_INST_ID_         NVARCHAR2(64),
    ACT_ID_                    NVARCHAR2(255),
    IS_ACTIVE_                 NUMBER(1)
        check (IS_ACTIVE_ IN (1, 0)),
    IS_CONCURRENT_             NUMBER(1)
        check (IS_CONCURRENT_ IN (1, 0)),
    IS_SCOPE_                  NUMBER(1)
        check (IS_SCOPE_ IN (1, 0)),
    IS_EVENT_SCOPE_            NUMBER(1)
        check (IS_EVENT_SCOPE_ IN (1, 0)),
    IS_MI_ROOT_                NUMBER(1)
        check (IS_MI_ROOT_ IN (1, 0)),
    SUSPENSION_STATE_          NUMBER,
    CACHED_ENT_STATE_          NUMBER,
    TENANT_ID_                 NVARCHAR2(255) default '',
    NAME_                      NVARCHAR2(255),
    START_ACT_ID_              NVARCHAR2(255),
    START_TIME_                TIMESTAMP(6),
    START_USER_ID_             NVARCHAR2(255),
    LOCK_TIME_                 TIMESTAMP(6),
    LOCK_OWNER_                NVARCHAR2(255),
    IS_COUNT_ENABLED_          NUMBER(1)
        check (IS_COUNT_ENABLED_ IN (1, 0)),
    EVT_SUBSCR_COUNT_          NUMBER,
    TASK_COUNT_                NUMBER,
    JOB_COUNT_                 NUMBER,
    TIMER_JOB_COUNT_           NUMBER,
    SUSP_JOB_COUNT_            NUMBER,
    DEADLETTER_JOB_COUNT_      NUMBER,
    EXTERNAL_WORKER_JOB_COUNT_ NUMBER,
    VAR_COUNT_                 NUMBER,
    ID_LINK_COUNT_             NUMBER,
    CALLBACK_ID_               NVARCHAR2(255),
    CALLBACK_TYPE_             NVARCHAR2(255),
    REFERENCE_ID_              NVARCHAR2(255),
    REFERENCE_TYPE_            NVARCHAR2(255),
    PROPAGATED_STAGE_INST_ID_  NVARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.ACT_RU_EVENT_SUBSCR
(
    ID_                  NVARCHAR2(64)  not null
        primary key,
    REV_                 NUMBER,
    EVENT_TYPE_          NVARCHAR2(255) not null,
    EVENT_NAME_          NVARCHAR2(255),
    EXECUTION_ID_        NVARCHAR2(64)
        constraint ACT_FK_EVENT_EXEC
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_INST_ID_        NVARCHAR2(64),
    ACTIVITY_ID_         NVARCHAR2(64),
    CONFIGURATION_       NVARCHAR2(255),
    CREATED_             TIMESTAMP(6)   not null,
    PROC_DEF_ID_         NVARCHAR2(64),
    SUB_SCOPE_ID_        NVARCHAR2(64),
    SCOPE_ID_            NVARCHAR2(64),
    SCOPE_DEFINITION_ID_ NVARCHAR2(64),
    SCOPE_TYPE_          NVARCHAR2(64),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EVENT_SUBSCR_CONFIG_
    on SYT_NEW_PORTAL.ACT_RU_EVENT_SUBSCR (CONFIGURATION_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EVENT_SUBSCR
    on SYT_NEW_PORTAL.ACT_RU_EVENT_SUBSCR (EXECUTION_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_TASK
(
    ID_                       NVARCHAR2(64) not null
        primary key,
    REV_                      NUMBER,
    EXECUTION_ID_             NVARCHAR2(64)
        constraint ACT_FK_TASK_EXE
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_INST_ID_             NVARCHAR2(64)
        constraint ACT_FK_TASK_PROCINST
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_              NVARCHAR2(64)
        constraint ACT_FK_TASK_PROCDEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    TASK_DEF_ID_              NVARCHAR2(64),
    SCOPE_ID_                 NVARCHAR2(255),
    SUB_SCOPE_ID_             NVARCHAR2(255),
    SCOPE_TYPE_               NVARCHAR2(255),
    SCOPE_DEFINITION_ID_      NVARCHAR2(255),
    PROPAGATED_STAGE_INST_ID_ NVARCHAR2(255),
    NAME_                     NVARCHAR2(255),
    PARENT_TASK_ID_           NVARCHAR2(64),
    DESCRIPTION_              NVARCHAR2(2000),
    TASK_DEF_KEY_             NVARCHAR2(255),
    OWNER_                    NVARCHAR2(255),
    ASSIGNEE_                 NVARCHAR2(255),
    DELEGATION_               NVARCHAR2(64),
    PRIORITY_                 NUMBER,
    CREATE_TIME_              TIMESTAMP(6),
    DUE_DATE_                 TIMESTAMP(6),
    CATEGORY_                 NVARCHAR2(255),
    SUSPENSION_STATE_         NUMBER,
    TENANT_ID_                NVARCHAR2(255) default '',
    FORM_KEY_                 NVARCHAR2(255),
    CLAIM_TIME_               TIMESTAMP(6),
    IS_COUNT_ENABLED_         NUMBER(1)
        check (IS_COUNT_ENABLED_ IN (1, 0)),
    VAR_COUNT_                NUMBER,
    ID_LINK_COUNT_            NUMBER,
    SUB_TASK_COUNT_           NUMBER
)
    /

create table SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK
(
    ID_                  NVARCHAR2(64) not null
        primary key,
    REV_                 NUMBER,
    GROUP_ID_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255),
    USER_ID_             NVARCHAR2(255),
    TASK_ID_             NVARCHAR2(64)
        constraint ACT_FK_TSKASS_TASK
        references SYT_NEW_PORTAL.ACT_RU_TASK,
    PROC_INST_ID_        NVARCHAR2(64)
        constraint ACT_FK_IDL_PROCINST
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_         NVARCHAR2(64)
        constraint ACT_FK_ATHRZ_PROCEDEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_IDENT_LNK_USER
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (USER_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_IDENT_LNK_GROUP
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (GROUP_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_IDENT_LNK_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_IDENT_LNK_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_IDENT_LNK_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TSKASS_TASK
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (TASK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_ATHRZ_PROCEDEF
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (PROC_DEF_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_IDL_PROCINST
    on SYT_NEW_PORTAL.ACT_RU_IDENTITYLINK (PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_CREATE
    on SYT_NEW_PORTAL.ACT_RU_TASK (CREATE_TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_TASK (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_TASK (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_TASK (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_EXEC
    on SYT_NEW_PORTAL.ACT_RU_TASK (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_PROCINST
    on SYT_NEW_PORTAL.ACT_RU_TASK (PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TASK_PROCDEF
    on SYT_NEW_PORTAL.ACT_RU_TASK (PROC_DEF_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_VARIABLE
(
    ID_           NVARCHAR2(64)  not null
        primary key,
    REV_          NUMBER,
    TYPE_         NVARCHAR2(255) not null,
    NAME_         NVARCHAR2(255) not null,
    EXECUTION_ID_ NVARCHAR2(64)
        constraint ACT_FK_VAR_EXE
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_INST_ID_ NVARCHAR2(64)
        constraint ACT_FK_VAR_PROCINST
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    TASK_ID_      NVARCHAR2(64),
    SCOPE_ID_     NVARCHAR2(255),
    SUB_SCOPE_ID_ NVARCHAR2(255),
    SCOPE_TYPE_   NVARCHAR2(255),
    BYTEARRAY_ID_ NVARCHAR2(64)
        constraint ACT_FK_VAR_BYTEARRAY
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    DOUBLE_       NUMBER,
    LONG_         NUMBER(19),
    TEXT_         NVARCHAR2(2000),
    TEXT2_        NVARCHAR2(2000)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_VAR_SCOPE_ID_TYPE
    on SYT_NEW_PORTAL.ACT_RU_VARIABLE (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_VAR_SUB_ID_TYPE
    on SYT_NEW_PORTAL.ACT_RU_VARIABLE (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_VAR_BYTEARRAY
    on SYT_NEW_PORTAL.ACT_RU_VARIABLE (BYTEARRAY_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_VARIABLE_TASK_ID
    on SYT_NEW_PORTAL.ACT_RU_VARIABLE (TASK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_VAR_EXE
    on SYT_NEW_PORTAL.ACT_RU_VARIABLE (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_VAR_PROCINST
    on SYT_NEW_PORTAL.ACT_RU_VARIABLE (PROC_INST_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_JOB
(
    ID_                  NVARCHAR2(64)  not null
        primary key,
    REV_                 NUMBER,
    CATEGORY_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255) not null,
    LOCK_EXP_TIME_       TIMESTAMP(6),
    LOCK_OWNER_          NVARCHAR2(255),
    EXCLUSIVE_           NUMBER(1)
        check (EXCLUSIVE_ IN (1, 0)),
    EXECUTION_ID_        NVARCHAR2(64)
        constraint ACT_FK_JOB_EXECUTION
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROCESS_INSTANCE_ID_ NVARCHAR2(64)
        constraint ACT_FK_JOB_PROCESS_INSTANCE
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_         NVARCHAR2(64)
        constraint ACT_FK_JOB_PROC_DEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    ELEMENT_ID_          NVARCHAR2(255),
    ELEMENT_NAME_        NVARCHAR2(255),
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    CORRELATION_ID_      NVARCHAR2(255),
    RETRIES_             NUMBER,
    EXCEPTION_STACK_ID_  NVARCHAR2(64)
        constraint ACT_FK_JOB_EXCEPTION
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    EXCEPTION_MSG_       NVARCHAR2(2000),
    DUEDATE_             TIMESTAMP(6),
    REPEAT_              NVARCHAR2(255),
    HANDLER_TYPE_        NVARCHAR2(255),
    HANDLER_CFG_         NVARCHAR2(2000),
    CUSTOM_VALUES_ID_    NVARCHAR2(64)
        constraint ACT_FK_JOB_CUSTOM_VAL
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    CREATE_TIME_         TIMESTAMP(6),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_EXCEPTION
    on SYT_NEW_PORTAL.ACT_RU_JOB (EXCEPTION_STACK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_CUSTOM_VAL_ID
    on SYT_NEW_PORTAL.ACT_RU_JOB (CUSTOM_VALUES_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_CORRELATION_ID
    on SYT_NEW_PORTAL.ACT_RU_JOB (CORRELATION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_JOB (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_JOB (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_JOB (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_EXECUTION_ID
    on SYT_NEW_PORTAL.ACT_RU_JOB (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_PROC_INST_ID
    on SYT_NEW_PORTAL.ACT_RU_JOB (PROCESS_INSTANCE_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_JOB_PROC_DEF_ID
    on SYT_NEW_PORTAL.ACT_RU_JOB (PROC_DEF_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_TIMER_JOB
(
    ID_                  NVARCHAR2(64)  not null
        primary key,
    REV_                 NUMBER,
    CATEGORY_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255) not null,
    LOCK_EXP_TIME_       TIMESTAMP(6),
    LOCK_OWNER_          NVARCHAR2(255),
    EXCLUSIVE_           NUMBER(1)
        check (EXCLUSIVE_ IN (1, 0)),
    EXECUTION_ID_        NVARCHAR2(64)
        constraint ACT_FK_TJOB_EXECUTION
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROCESS_INSTANCE_ID_ NVARCHAR2(64)
        constraint ACT_FK_TJOB_PROCESS_INSTANCE
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_         NVARCHAR2(64)
        constraint ACT_FK_TJOB_PROC_DEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    ELEMENT_ID_          NVARCHAR2(255),
    ELEMENT_NAME_        NVARCHAR2(255),
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    CORRELATION_ID_      NVARCHAR2(255),
    RETRIES_             NUMBER,
    EXCEPTION_STACK_ID_  NVARCHAR2(64)
        constraint ACT_FK_TJOB_EXCEPTION
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    EXCEPTION_MSG_       NVARCHAR2(2000),
    DUEDATE_             TIMESTAMP(6),
    REPEAT_              NVARCHAR2(255),
    HANDLER_TYPE_        NVARCHAR2(255),
    HANDLER_CFG_         NVARCHAR2(2000),
    CUSTOM_VALUES_ID_    NVARCHAR2(64)
        constraint ACT_FK_TJOB_CUSTOM_VAL
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    CREATE_TIME_         TIMESTAMP(6),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_EXCEPTION
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (EXCEPTION_STACK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_CUSTOM_VAL_ID
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (CUSTOM_VALUES_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_CORRELATION_ID
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (CORRELATION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_EXECUTION_ID
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_PROC_INST_ID
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (PROCESS_INSTANCE_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_TJOB_PROC_DEF_ID
    on SYT_NEW_PORTAL.ACT_RU_TIMER_JOB (PROC_DEF_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB
(
    ID_                  NVARCHAR2(64)  not null
        primary key,
    REV_                 NUMBER,
    CATEGORY_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255) not null,
    EXCLUSIVE_           NUMBER(1)
        check (EXCLUSIVE_ IN (1, 0)),
    EXECUTION_ID_        NVARCHAR2(64)
        constraint ACT_FK_SJOB_EXECUTION
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROCESS_INSTANCE_ID_ NVARCHAR2(64)
        constraint ACT_FK_SJOB_PROCESS_INSTANCE
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_         NVARCHAR2(64)
        constraint ACT_FK_SJOB_PROC_DEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    ELEMENT_ID_          NVARCHAR2(255),
    ELEMENT_NAME_        NVARCHAR2(255),
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    CORRELATION_ID_      NVARCHAR2(255),
    RETRIES_             NUMBER,
    EXCEPTION_STACK_ID_  NVARCHAR2(64)
        constraint ACT_FK_SJOB_EXCEPTION
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    EXCEPTION_MSG_       NVARCHAR2(2000),
    DUEDATE_             TIMESTAMP(6),
    REPEAT_              NVARCHAR2(255),
    HANDLER_TYPE_        NVARCHAR2(255),
    HANDLER_CFG_         NVARCHAR2(2000),
    CUSTOM_VALUES_ID_    NVARCHAR2(64)
        constraint ACT_FK_SJOB_CUSTOM_VAL
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    CREATE_TIME_         TIMESTAMP(6),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_EXCEPTION
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (EXCEPTION_STACK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_CUSTOM_VAL_ID
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (CUSTOM_VALUES_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_CORRELATION_ID
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (CORRELATION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_EXECUTION_ID
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_PROC_INST_ID
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (PROCESS_INSTANCE_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_SJOB_PROC_DEF_ID
    on SYT_NEW_PORTAL.ACT_RU_SUSPENDED_JOB (PROC_DEF_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB
(
    ID_                  NVARCHAR2(64)  not null
        primary key,
    REV_                 NUMBER,
    CATEGORY_            NVARCHAR2(255),
    TYPE_                NVARCHAR2(255) not null,
    EXCLUSIVE_           NUMBER(1)
        check (EXCLUSIVE_ IN (1, 0)),
    EXECUTION_ID_        NVARCHAR2(64)
        constraint ACT_FK_DJOB_EXECUTION
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROCESS_INSTANCE_ID_ NVARCHAR2(64)
        constraint ACT_FK_DJOB_PROCESS_INSTANCE
        references SYT_NEW_PORTAL.ACT_RU_EXECUTION,
    PROC_DEF_ID_         NVARCHAR2(64)
        constraint ACT_FK_DJOB_PROC_DEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    ELEMENT_ID_          NVARCHAR2(255),
    ELEMENT_NAME_        NVARCHAR2(255),
    SCOPE_ID_            NVARCHAR2(255),
    SUB_SCOPE_ID_        NVARCHAR2(255),
    SCOPE_TYPE_          NVARCHAR2(255),
    SCOPE_DEFINITION_ID_ NVARCHAR2(255),
    CORRELATION_ID_      NVARCHAR2(255),
    EXCEPTION_STACK_ID_  NVARCHAR2(64)
        constraint ACT_FK_DJOB_EXCEPTION
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    EXCEPTION_MSG_       NVARCHAR2(2000),
    DUEDATE_             TIMESTAMP(6),
    REPEAT_              NVARCHAR2(255),
    HANDLER_TYPE_        NVARCHAR2(255),
    HANDLER_CFG_         NVARCHAR2(2000),
    CUSTOM_VALUES_ID_    NVARCHAR2(64)
        constraint ACT_FK_DJOB_CUSTOM_VAL
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY,
    CREATE_TIME_         TIMESTAMP(6),
    TENANT_ID_           NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_EXCEPTION
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (EXCEPTION_STACK_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_CUSTOM_VAL_ID
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (CUSTOM_VALUES_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_CORRELATION_ID
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (CORRELATION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_SUB_SCOPE
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (SUB_SCOPE_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_SCOPE_DEF
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (SCOPE_DEFINITION_ID_, SCOPE_TYPE_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_EXECUTION_ID
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_PROC_INST_ID
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (PROCESS_INSTANCE_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_DJOB_PROC_DEF_ID
    on SYT_NEW_PORTAL.ACT_RU_DEADLETTER_JOB (PROC_DEF_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EXEC_BUSKEY
    on SYT_NEW_PORTAL.ACT_RU_EXECUTION (BUSINESS_KEY_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EXEC_ROOT
    on SYT_NEW_PORTAL.ACT_RU_EXECUTION (ROOT_PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EXE_PROCINST
    on SYT_NEW_PORTAL.ACT_RU_EXECUTION (PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EXE_PARENT
    on SYT_NEW_PORTAL.ACT_RU_EXECUTION (PARENT_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EXE_SUPER
    on SYT_NEW_PORTAL.ACT_RU_EXECUTION (SUPER_EXEC_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_EXE_PROCDEF
    on SYT_NEW_PORTAL.ACT_RU_EXECUTION (PROC_DEF_ID_)
    /

create table SYT_NEW_PORTAL.ACT_EVT_LOG
(
    LOG_NR_       NUMBER(19)   not null
        primary key,
    TYPE_         NVARCHAR2(64),
    PROC_DEF_ID_  NVARCHAR2(64),
    PROC_INST_ID_ NVARCHAR2(64),
    EXECUTION_ID_ NVARCHAR2(64),
    TASK_ID_      NVARCHAR2(64),
    TIME_STAMP_   TIMESTAMP(6) not null,
    USER_ID_      NVARCHAR2(255),
    DATA_         BLOB,
    LOCK_OWNER_   NVARCHAR2(255),
    LOCK_TIME_    TIMESTAMP(6),
    IS_PROCESSED_ NUMBER(3) default 0
)
    /

create table SYT_NEW_PORTAL.ACT_PROCDEF_INFO
(
    ID_           NVARCHAR2(64) not null
        primary key,
    PROC_DEF_ID_  NVARCHAR2(64) not null
        constraint ACT_FK_INFO_PROCDEF
        references SYT_NEW_PORTAL.ACT_RE_PROCDEF,
    REV_          NUMBER,
    INFO_JSON_ID_ NVARCHAR2(64)
        constraint ACT_FK_INFO_JSON_BA
        references SYT_NEW_PORTAL.ACT_GE_BYTEARRAY
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_PROCDEF_INFO_JSON
    on SYT_NEW_PORTAL.ACT_PROCDEF_INFO (INFO_JSON_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_PROCDEF_INFO_PROC
    on SYT_NEW_PORTAL.ACT_PROCDEF_INFO (PROC_DEF_ID_)
    /

alter table SYT_NEW_PORTAL.ACT_PROCDEF_INFO
    add constraint ACT_UNIQ_INFO_PROCDEF
        unique (PROC_DEF_ID_)
    /

create table SYT_NEW_PORTAL.ACT_RU_ACTINST
(
    ID_                NVARCHAR2(64)  not null
        primary key,
    REV_               NUMBER         default 1,
    PROC_DEF_ID_       NVARCHAR2(64)  not null,
    PROC_INST_ID_      NVARCHAR2(64)  not null,
    EXECUTION_ID_      NVARCHAR2(64)  not null,
    ACT_ID_            NVARCHAR2(255) not null,
    TASK_ID_           NVARCHAR2(64),
    CALL_PROC_INST_ID_ NVARCHAR2(64),
    ACT_NAME_          NVARCHAR2(255),
    ACT_TYPE_          NVARCHAR2(255) not null,
    ASSIGNEE_          NVARCHAR2(255),
    START_TIME_        TIMESTAMP(6)   not null,
    END_TIME_          TIMESTAMP(6),
    DURATION_          NUMBER(19),
    TRANSACTION_ORDER_ NUMBER,
    DELETE_REASON_     NVARCHAR2(2000),
    TENANT_ID_         NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_ACTI_START
    on SYT_NEW_PORTAL.ACT_RU_ACTINST (START_TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_ACTI_END
    on SYT_NEW_PORTAL.ACT_RU_ACTINST (END_TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_ACTI_PROC
    on SYT_NEW_PORTAL.ACT_RU_ACTINST (PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_ACTI_PROC_ACT
    on SYT_NEW_PORTAL.ACT_RU_ACTINST (PROC_INST_ID_, ACT_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_ACTI_EXEC
    on SYT_NEW_PORTAL.ACT_RU_ACTINST (EXECUTION_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_RU_ACTI_EXEC_ACT
    on SYT_NEW_PORTAL.ACT_RU_ACTINST (EXECUTION_ID_, ACT_ID_)
    /

create table SYT_NEW_PORTAL.ACT_HI_PROCINST
(
    ID_                        NVARCHAR2(64) not null
        primary key,
    REV_                       NUMBER         default 1,
    PROC_INST_ID_              NVARCHAR2(64) not null
        unique,
    BUSINESS_KEY_              NVARCHAR2(255),
    PROC_DEF_ID_               NVARCHAR2(64) not null,
    START_TIME_                TIMESTAMP(6)  not null,
    END_TIME_                  TIMESTAMP(6),
    DURATION_                  NUMBER(19),
    START_USER_ID_             NVARCHAR2(255),
    START_ACT_ID_              NVARCHAR2(255),
    END_ACT_ID_                NVARCHAR2(255),
    SUPER_PROCESS_INSTANCE_ID_ NVARCHAR2(64),
    DELETE_REASON_             NVARCHAR2(2000),
    TENANT_ID_                 NVARCHAR2(255) default '',
    NAME_                      NVARCHAR2(255),
    CALLBACK_ID_               NVARCHAR2(255),
    CALLBACK_TYPE_             NVARCHAR2(255),
    REFERENCE_ID_              NVARCHAR2(255),
    REFERENCE_TYPE_            NVARCHAR2(255)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_PRO_INST_END
    on SYT_NEW_PORTAL.ACT_HI_PROCINST (END_TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_PRO_I_BUSKEY
    on SYT_NEW_PORTAL.ACT_HI_PROCINST (BUSINESS_KEY_)
    /

create table SYT_NEW_PORTAL.ACT_HI_ACTINST
(
    ID_                NVARCHAR2(64)  not null
        primary key,
    REV_               NUMBER         default 1,
    PROC_DEF_ID_       NVARCHAR2(64)  not null,
    PROC_INST_ID_      NVARCHAR2(64)  not null,
    EXECUTION_ID_      NVARCHAR2(64)  not null,
    ACT_ID_            NVARCHAR2(255) not null,
    TASK_ID_           NVARCHAR2(64),
    CALL_PROC_INST_ID_ NVARCHAR2(64),
    ACT_NAME_          NVARCHAR2(255),
    ACT_TYPE_          NVARCHAR2(255) not null,
    ASSIGNEE_          NVARCHAR2(255),
    START_TIME_        TIMESTAMP(6)   not null,
    END_TIME_          TIMESTAMP(6),
    TRANSACTION_ORDER_ NUMBER,
    DURATION_          NUMBER(19),
    DELETE_REASON_     NVARCHAR2(2000),
    TENANT_ID_         NVARCHAR2(255) default ''
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ACT_INST_START
    on SYT_NEW_PORTAL.ACT_HI_ACTINST (START_TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ACT_INST_END
    on SYT_NEW_PORTAL.ACT_HI_ACTINST (END_TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ACT_INST_PROCINST
    on SYT_NEW_PORTAL.ACT_HI_ACTINST (PROC_INST_ID_, ACT_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_ACT_INST_EXEC
    on SYT_NEW_PORTAL.ACT_HI_ACTINST (EXECUTION_ID_, ACT_ID_)
    /

create table SYT_NEW_PORTAL.ACT_HI_DETAIL
(
    ID_           NVARCHAR2(64)  not null
        primary key,
    TYPE_         NVARCHAR2(255) not null,
    PROC_INST_ID_ NVARCHAR2(64),
    EXECUTION_ID_ NVARCHAR2(64),
    TASK_ID_      NVARCHAR2(64),
    ACT_INST_ID_  NVARCHAR2(64),
    NAME_         NVARCHAR2(255) not null,
    VAR_TYPE_     NVARCHAR2(64),
    REV_          NUMBER,
    TIME_         TIMESTAMP(6)   not null,
    BYTEARRAY_ID_ NVARCHAR2(64),
    DOUBLE_       NUMBER,
    LONG_         NUMBER(19),
    TEXT_         NVARCHAR2(2000),
    TEXT2_        NVARCHAR2(2000)
)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_DETAIL_PROC_INST
    on SYT_NEW_PORTAL.ACT_HI_DETAIL (PROC_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_DETAIL_ACT_INST
    on SYT_NEW_PORTAL.ACT_HI_DETAIL (ACT_INST_ID_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_DETAIL_TIME
    on SYT_NEW_PORTAL.ACT_HI_DETAIL (TIME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_DETAIL_NAME
    on SYT_NEW_PORTAL.ACT_HI_DETAIL (NAME_)
    /

create index SYT_NEW_PORTAL.ACT_IDX_HI_DETAIL_TASK_ID
    on SYT_NEW_PORTAL.ACT_HI_DETAIL (TASK_ID_)
    /

create table SYT_NEW_PORTAL.ACT_HI_COMMENT
(
    ID_           NVARCHAR2(64) not null
        primary key,
    TYPE_         NVARCHAR2(255),
    TIME_         TIMESTAMP(6)  not null,
    USER_ID_      NVARCHAR2(255),
    TASK_ID_      NVARCHAR2(64),
    PROC_INST_ID_ NVARCHAR2(64),
    ACTION_       NVARCHAR2(255),
    MESSAGE_      NVARCHAR2(2000),
    FULL_MSG_     BLOB
)
    /

create table SYT_NEW_PORTAL.ACT_HI_ATTACHMENT
(
    ID_           NVARCHAR2(64) not null
        primary key,
    REV_          NUMBER,
    USER_ID_      NVARCHAR2(255),
    NAME_         NVARCHAR2(255),
    DESCRIPTION_  NVARCHAR2(2000),
    TYPE_         NVARCHAR2(255),
    TASK_ID_      NVARCHAR2(64),
    PROC_INST_ID_ NVARCHAR2(64),
    URL_          NVARCHAR2(2000),
    CONTENT_ID_   NVARCHAR2(64),
    TIME_         TIMESTAMP(6)
)
    /

create table SYT_NEW_PORTAL.BPM_FORM_VARIABLE
(
    ID     VARCHAR2(50),
    NAME   VARCHAR2(255),
    VALUE  VARCHAR2(255),
    STATUS VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.BPM_FORM_VARIABLE is '表单变量'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_VARIABLE.NAME is '变量名称'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_VARIABLE.VALUE is '变量值'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_VARIABLE.STATUS is '状态'
/

create table SYT_NEW_PORTAL.SYT_HALL_STATISTICS
(
    ID           VARCHAR2(255 char) not null
        constraint SYS_C0016139
        check ("ID" IS NOT NULL)
        constraint SYS_C0016140
        check ("ID" IS NOT NULL)
        constraint SYS_C0016141
        check ("ID" IS NOT NULL),
    PARENT_ID    VARCHAR2(255 char),
    NAME         VARCHAR2(255 char),
    REMARK       VARCHAR2(255 char),
    SORT         NUMBER(10),
    STATUS       VARCHAR2(255 char),
    CLICK_URL    VARCHAR2(255 char),
    DATA_SOURCES VARCHAR2(255 char),
    MORE_URL     VARCHAR2(255 char),
    ROLEIDS      CLOB,
    ORGIDS       CLOB,
    DETAIL_SHOW  VARCHAR2(255 char)
)
    /

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.PARENT_ID is '父类ID'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.STATUS is '状态, 启用或禁用'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.CLICK_URL is '点击地址'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.DATA_SOURCES is '数据来源'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.MORE_URL is '更多地址'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.ROLEIDS is '可见角色'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.ORGIDS is '可见组织机构'
/

comment on column SYT_NEW_PORTAL.SYT_HALL_STATISTICS.DETAIL_SHOW is '详情页面是否显示：为“是”时，可在详情页面显示'
/

create table SYT_NEW_PORTAL.MAGIC_API_FILE
(
    FILE_PATH    VARCHAR2(4000) not null
        constraint MAGIC_API_FILE_PK
        primary key,
    FILE_CONTENT CLOB
)
    /

create table SYT_NEW_PORTAL.SYT_ADDRESS_BOOK
(
    HUMANCODE      VARCHAR2(255),
    ID             VARCHAR2(255) not null
        primary key,
    HUMANNAME      VARCHAR2(255),
    DEPARTMENT     VARCHAR2(255),
    TELEPHONE      VARCHAR2(255),
    MOBILEPHONE    VARCHAR2(255),
    OFFICELOCATION VARCHAR2(255),
    SORT           NUMBER,
    GWMC           VARCHAR2(255),
    CREATEDATE     TIMESTAMP(6),
    ROWINDEX       NUMBER(10)
)
    /

comment on table SYT_NEW_PORTAL.SYT_ADDRESS_BOOK is '通讯录'
/

create table SYT_NEW_PORTAL.SYT_CODE_FLB
(
    ID   VARCHAR2(50 char) not null,
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_RESOUR_PROJECT
(
    ID                     VARCHAR2(255 char) not null,
    CATEGORY_ID            VARCHAR2(255 char),
    CREATE_TIME            TIMESTAMP(6),
    CREATE_USER_CODE       VARCHAR2(255 char),
    CREATE_USER_NAME       VARCHAR2(255 char),
    SFCD                   VARCHAR2(255 char),
    KRNRS                  NUMBER,
    SORT                   NUMBER,
    NAME                   VARCHAR2(255 char),
    REMARK                 CLOB,
    START_TIME             TIMESTAMP(6),
    END_TIME               TIMESTAMP(6),
    PROCESS_DEFINITION_KEY VARCHAR2(255),
    SFSH                   VARCHAR2(255),
    FSSS                   VARCHAR2(255),
    LXR                    VARCHAR2(255),
    LXRDH                  VARCHAR2(255),
    YYGS                   VARCHAR2(255) default 0
)
    /

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.ID is '项目ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.CATEGORY_ID is '分类'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.CREATE_TIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.CREATE_USER_CODE is '创建人账号'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.CREATE_USER_NAME is '创建人姓名'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.SFCD is '是否场地（是/否，为”是“时，设置可容纳人数）'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.KRNRS is '可容纳人数'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.NAME is '项目名称'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.START_TIME is '申请开始时间'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.END_TIME is '申请结束时间'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.PROCESS_DEFINITION_KEY is '工作流程定义标识'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.SFSH is '是否审核（是：需要选择审核流程）'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.FSSS is '附属设施'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.LXR is '联系人'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.LXRDH is '联系人电话'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT.YYGS is '预约格式(0-单人单占式，1-多人共占式)'
/

create table SYT_NEW_PORTAL.SYT_RESOUR_PROJECT_TIME
(
    ID           VARCHAR2(255 char) not null,
    PROJECT_ID   VARCHAR2(255 char),
    REMARK       CLOB,
    TIME_QUANTUN VARCHAR2(50),
    SORT         NUMBER(10)
)
    /

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT_TIME.ID is '时间片ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT_TIME.PROJECT_ID is '公共资源管理项目'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT_TIME.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT_TIME.TIME_QUANTUN is '时间段'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_PROJECT_TIME.SORT is '排序'
/

create table SYT_NEW_PORTAL.SYT_RESOUR_APPLY
(
    ID              VARCHAR2(255 char) not null,
    PROJECT_ID      VARCHAR2(255 char),
    APPLY_USER_CODE VARCHAR2(255 char),
    APPLY_USER_NAME VARCHAR2(255 char),
    PROCESS_INST_ID VARCHAR2(255),
    CREATE_TIME     TIMESTAMP(6),
    UPDATE_TIME     TIMESTAMP(6),
    RESULT          VARCHAR2(255 char),
    BZ              VARCHAR2(255),
    APPLYREASON     VARCHAR2(255),
    FORMVARIABLES   CLOB
)
    /

comment on table SYT_NEW_PORTAL.SYT_RESOUR_APPLY is '资源申请表'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.ID is '申请ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.PROJECT_ID is '项目ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.APPLY_USER_CODE is '申请人账号'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.APPLY_USER_NAME is '申请人姓名'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.PROCESS_INST_ID is '流程实例ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.CREATE_TIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.UPDATE_TIME is '更新时间'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.RESULT is '结果'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.BZ is '备注（可记录删除、取消原因）'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.APPLYREASON is '申请理由'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY.FORMVARIABLES is '表单内容'
/

create table SYT_NEW_PORTAL.SYTRESOURPROJECT
(
    ID                   VARCHAR2(255 char) not null
        primary key,
    CATEGORYID           VARCHAR2(255 char),
    CREATETIME           TIMESTAMP(6),
    CREATEUSERCODE       VARCHAR2(255 char),
    CREATEUSERNAME       VARCHAR2(255 char),
    ENDTIME              TIMESTAMP(6),
    KRNRS                NUMBER(10),
    NAME                 VARCHAR2(255 char),
    PROCESSDEFINITIONKEY VARCHAR2(255 char),
    REMARK               VARCHAR2(255 char),
    SFCD                 VARCHAR2(255 char),
    SORT                 NUMBER(10),
    STARTTIME            TIMESTAMP(6)
)
    /

create table SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME
(
    ID              VARCHAR2(255 char) not null,
    PROJECT_ID      VARCHAR2(255 char),
    APPLY_ID        VARCHAR2(255),
    DATEDAY         VARCHAR2(255 char),
    TIME_QUANTUN    VARCHAR2(255 char),
    APPLY_DATE_TIME VARCHAR2(255 char),
    STATUS          VARCHAR2(255),
    HOLD_NUMBER     NUMBER default 0
)
    /

comment on table SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME is '申请的时间段'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.ID is '申请ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.PROJECT_ID is '项目ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.APPLY_ID is '申请记录ID'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.DATEDAY is '申请的日期'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.TIME_QUANTUN is '申请的时间段'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.APPLY_DATE_TIME is '申请的日期时间段'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.STATUS is '占用状态（是/否）'
/

comment on column SYT_NEW_PORTAL.SYT_RESOUR_APPLY_TIME.HOLD_NUMBER is '申请数'
/

create table SYT_NEW_PORTAL.SYT_APPLET
(
    ID           VARCHAR2(255 char) not null
        primary key,
    APPID        VARCHAR2(255 char),
    COLUMNS      VARCHAR2(255 char),
    DATA_URL     VARCHAR2(255 char),
    DESCRIPTION  VARCHAR2(255 char),
    DETAIL_URL   VARCHAR2(255 char),
    IMG_URL      VARCHAR2(255 char),
    NAME         VARCHAR2(255 char),
    REDIRECT_URL VARCHAR2(255 char),
    SORT         NUMBER(19),
    STATUS       VARCHAR2(255 char),
    STYLE        VARCHAR2(255 char),
    TYPE         VARCHAR2(255 char),
    ROLE_IDS     CLOB
)
    /

create table SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE
(
    ID                VARCHAR2(50) not null
        primary key,
    ATTRIBUTE_NAME    VARCHAR2(255),
    VALUE_TYPE        VARCHAR2(255),
    RETURN_VALUE_TYPE VARCHAR2(255),
    BELONG_ORG        VARCHAR2(255),
    BELONG_ORG_NAME   VARCHAR2(255),
    REMARK            VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE is '流程组织关系管理'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE.ATTRIBUTE_NAME is '属性名称'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE.VALUE_TYPE is '机构类型'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE.RETURN_VALUE_TYPE is '返回值类型'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE.BELONG_ORG is '所属机构id'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE.BELONG_ORG_NAME is '所属机构名称'
/

create table SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG
(
    ID                   VARCHAR2(50) not null
        primary key,
    ATTRIBUTE_ID         VARCHAR2(255)
        constraint BPM_ORG_ATTRIBUTE_CONFIG_FK
        references SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE,
    ATTRIBUTE_VALUE      VARCHAR2(4000),
    ORG                  VARCHAR2(255),
    ORG_NAME             VARCHAR2(255),
    ORG_TYPE             VARCHAR2(255),
    VALUE_TYPE           VARCHAR2(255),
    OPERATOR             VARCHAR2(255),
    OPERATOR_TIME        TIMESTAMP(6),
    ATTRIBUTE_NAME       VARCHAR2(255 char),
    ATTRIBUTE_VALUE_NAME VARCHAR2(255 char)
)
    /

comment on table SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG is '流程组织关系配置表'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ATTRIBUTE_ID is '属性标识'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ATTRIBUTE_VALUE is '返回值value'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ORG is '组织关系value'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ORG_NAME is '组织关系label'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ORG_TYPE is '组织关系类型'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.VALUE_TYPE is '返回值类型'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.OPERATOR is '操作人'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.OPERATOR_TIME is '操作时间'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ATTRIBUTE_NAME is '属性名称'
/

comment on column SYT_NEW_PORTAL.BPM_ORG_ATTRIBUTE_CONFIG.ATTRIBUTE_VALUE_NAME is '返回值label'
/

create table SYT_NEW_PORTAL.BPM_COMMENT_TEMPLATE
(
    ID     VARCHAR2(50) not null
        primary key,
    USERID VARCHAR2(255),
    REASON VARCHAR2(2000),
    SORT   NUMBER
)
    /

comment on table SYT_NEW_PORTAL.BPM_COMMENT_TEMPLATE is '审批意见模板'
/

comment on column SYT_NEW_PORTAL.BPM_COMMENT_TEMPLATE.USERID is '用户账号'
/

comment on column SYT_NEW_PORTAL.BPM_COMMENT_TEMPLATE.REASON is '审批建议'
/

comment on column SYT_NEW_PORTAL.BPM_COMMENT_TEMPLATE.SORT is '排序'
/

create table SYT_NEW_PORTAL.SYT_CODE_TBRW
(
    ID              VARCHAR2(50 char) not null,
    BZ              VARCHAR2(255 char),
    CODE            VARCHAR2(255 char),
    NAME            VARCHAR2(255 char),
    CRON_EXPRESSION VARCHAR2(255),
    CRONEXPRESSION  VARCHAR2(255 char),
    TYPE            VARCHAR2(50)
)
    /

comment on column SYT_NEW_PORTAL.SYT_CODE_TBRW.TYPE is '周期类型（0-年，1-月，2-周，3-日）'
/

create table SYT_NEW_PORTAL.SYT_TASK_PROJECT
(
    ID                     VARCHAR2(255 char) not null,
    CATEGORY_ID            VARCHAR2(255 char),
    CREATE_TIME            TIMESTAMP(6),
    CREATE_USER_CODE       VARCHAR2(255 char),
    CREATE_USER_NAME       VARCHAR2(255 char),
    SORT                   NUMBER,
    NAME                   VARCHAR2(255 char),
    REMARK                 CLOB,
    START_TIME             TIMESTAMP(6),
    END_TIME               TIMESTAMP(6),
    PROCESS_DEFINITION_KEY VARCHAR2(255),
    STYLE                  VARCHAR2(255),
    FORM_ID                VARCHAR2(255),
    ROLE_LIST              VARCHAR2(255),
    RUN_STATUS             VARCHAR2(255) default 1,
    CRON                   VARCHAR2(50),
    IS_STYLE               VARCHAR2(50),
    CRON_RUN               VARCHAR2(50)  default 1,
    REMIND_MESSAGE         VARCHAR2(255),
    PARENT_ID              VARCHAR2(255),
    TESTC                  CLOB,
    ADMIN_USER_CODE        VARCHAR2(255),
    ADMIN_USER_NAME        VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_TASK_PROJECT is '任务填报项目'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.ID is '项目ID'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.CATEGORY_ID is '分类'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.CREATE_TIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.CREATE_USER_CODE is '创建人账号'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.CREATE_USER_NAME is '创建人姓名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.NAME is '项目名称'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.START_TIME is '申请开始时间'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.END_TIME is '申请结束时间'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.PROCESS_DEFINITION_KEY is '工作流程定义标识'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.STYLE is '任务方式（区分是表单，还是流程+表单）'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.FORM_ID is '填报表单'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.RUN_STATUS is '暂停状态'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.CRON is 'cron表达式'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.IS_STYLE is '是否周期(0-非周期，1-周期，2-周期子）'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.CRON_RUN is '是否开启定时任务'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.REMIND_MESSAGE is '提示消息'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.PARENT_ID is '父id'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.ADMIN_USER_CODE is '任务管理员账号'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PROJECT.ADMIN_USER_NAME is '任务管理员姓名'
/

create table SYT_NEW_PORTAL.SYT_FILE_RESOURCE
(
    ID         VARCHAR2(50 char) not null
        primary key,
    NAME       VARCHAR2(255 char),
    STATUS     VARCHAR2(255),
    FILEID     VARCHAR2(255),
    REMARK     VARCHAR2(255),
    CREATETIME TIMESTAMP(6),
    UPDATETIME TIMESTAMP(6),
    SORT       NUMBER
)
    /

comment on table SYT_NEW_PORTAL.SYT_FILE_RESOURCE is '常用文件管理'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.NAME is '文件名称'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.STATUS is '状态(启用/停用)'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.FILEID is '文件ID'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.CREATETIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.UPDATETIME is '更新时间'
/

comment on column SYT_NEW_PORTAL.SYT_FILE_RESOURCE.SORT is '排序'
/

create table SYT_NEW_PORTAL.SYT_TASK_PERSON
(
    ID          VARCHAR2(255 char) not null
        primary key,
    PROJECT_ID  VARCHAR2(255 char),
    USER_CODE   VARCHAR2(255 char),
    USER_NAME   VARCHAR2(255 char),
    STATUS      VARCHAR2(255),
    READ_STATUS VARCHAR2(255) default 0
)
    /

comment on table SYT_NEW_PORTAL.SYT_TASK_PERSON is '任务填报人员名单'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PERSON.PROJECT_ID is '项目ID'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PERSON.USER_CODE is '账号'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PERSON.USER_NAME is '姓名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PERSON.STATUS is 'F表示未填报；T已填报'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_PERSON.READ_STATUS is '已读状态'
/

create table SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD
(
    ID              VARCHAR2(255 char) not null,
    PROJECT_ID      VARCHAR2(255 char),
    USER_CODE       VARCHAR2(255 char),
    USER_NAME       VARCHAR2(255 char),
    PROCESS_INST_ID VARCHAR2(255),
    CREATE_TIME     TIMESTAMP(6),
    UPDATE_TIME     TIMESTAMP(6),
    RESULT          VARCHAR2(255 char),
    FORMVARIABLES   CLOB
)
    /

comment on table SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD is '任务填报记录'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.ID is '申请ID'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.PROJECT_ID is '项目ID'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.USER_CODE is '填报人账号'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.USER_NAME is '填报人姓名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.PROCESS_INST_ID is '流程实例ID'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.CREATE_TIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.UPDATE_TIME is '更新时间'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.RESULT is '结果'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_FILL_RECORD.FORMVARIABLES is '表单内容'
/

create table SYT_NEW_PORTAL.SYTTASKPROJECT_ORGIDLIST
(
    SYTTASKPROJECT_ID VARCHAR2(255 char) not null,
    ORGIDLIST         VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYTTASKPROJECT_ROLELIST
(
    SYTTASKPROJECT_ID VARCHAR2(255 char) not null,
    ROLELIST          VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYTTASKPROJECT_USERLIST
(
    SYTTASKPROJECT_ID VARCHAR2(255 char) not null,
    USERLIST          VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.MH_CMS_DBXX
(
    F_INFO_ID      VARCHAR2(50),
    F_TITLE        VARCHAR2(255),
    F_PUBLISH_DATE DATE,
    F_SITE_ID      VARCHAR2(50),
    F_NODE_ID      VARCHAR2(50),
    F_NAME         VARCHAR2(255),
    F_STATUS       VARCHAR2(255),
    F_ROLE_ID      VARCHAR2(50),
    F_ROLE_NAME    VARCHAR2(255),
    F_DESCRIPTION  VARCHAR2(255),
    F_USER_ID      VARCHAR2(50),
    F_USERNAME     VARCHAR2(255),
    F_REAL_NAME    VARCHAR2(255)
)
    /

create table SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL
(
    ID         VARCHAR2(50),
    NAME       VARCHAR2(255),
    URL        VARCHAR2(255),
    SFMR       VARCHAR2(255),
    HUMANCODE  VARCHAR2(255),
    HUMANNAME  VARCHAR2(255),
    CREATETIME TIMESTAMP(6)
)
    /

comment on table SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL is '电子印章'
/

comment on column SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL.NAME is '印章名称'
/

comment on column SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL.URL is '印章地址'
/

comment on column SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL.SFMR is '是否默认'
/

comment on column SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL.HUMANCODE is '所属人'
/

comment on column SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL.HUMANNAME is '所属人姓名'
/

comment on column SYT_NEW_PORTAL.SYT_ELECTRONIC_SEAL.CREATETIME is '创建时间'
/

create table SYT_NEW_PORTAL.BPM_FORM_FIELD
(
    ID                    VARCHAR2(50) not null
        primary key,
    FIELD_EN              VARCHAR2(255),
    FIELD_ZH              VARCHAR2(255),
    SORT                  NUMBER(10),
    PROCESS_DEFINITION_ID VARCHAR2(255),
    FORM_ID               VARCHAR2(255),
    FIELD_TYPE            VARCHAR2(255 char)
)
    /

comment on table SYT_NEW_PORTAL.BPM_FORM_FIELD is '表单对应的字段'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD.FIELD_EN is '表单字段'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD.FIELD_ZH is '表单字段中文名称'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD.PROCESS_DEFINITION_ID is '流程定义ID（关联流程的时候使用）'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD.FORM_ID is '表单ID（只关联表单的业务模块使用）'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD.FIELD_TYPE is '表单字段类型'
/

create table SYT_NEW_PORTAL.BPM_CUSTOM_COLUMN
(
    ID         VARCHAR2(255 char) not null
        primary key,
    FIELD_EN   VARCHAR2(255 char),
    FIELD_ZH   VARCHAR2(255 char),
    PROJECT_ID VARCHAR2(255 char),
    SORT       NUMBER(19),
    TYPE       VARCHAR2(255 char),
    FIELD_TYPE VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.BPM_CUSTOM_COLUMN.FIELD_TYPE is '字段类型'
/

create table SYT_NEW_PORTAL.SYT_TASK_CRON
(
    ID                 VARCHAR2(50),
    JOB_NAME           VARCHAR2(255),
    JOB_GROUP_NAME     VARCHAR2(255),
    TRIGGER_NAME       VARCHAR2(255),
    TRIGGER_GROUP_NAME VARCHAR2(255),
    CRON               VARCHAR2(50),
    JOB_DATA_MAP       CLOB
)
    /

comment on table SYT_NEW_PORTAL.SYT_TASK_CRON is '周期性创建填报项目'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.ID is 'id'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.JOB_NAME is '任务名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.JOB_GROUP_NAME is '任务所属组名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.TRIGGER_NAME is '触发器名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.TRIGGER_GROUP_NAME is '触发器所属组名'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.CRON is 'cron定时策略'
/

comment on column SYT_NEW_PORTAL.SYT_TASK_CRON.JOB_DATA_MAP is '任务执行参数'
/

create table SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE
(
    ID                 VARCHAR2(50) not null
        primary key,
    FIELD_EN           VARCHAR2(255),
    FIELD_ZH           VARCHAR2(255),
    RECORD_ID          VARCHAR2(255),
    HUMAN_CODE         VARCHAR2(255 char),
    HUMAN_NAME         VARCHAR2(255 char),
    ORGANIZATION_NAMES VARCHAR2(255 char),
    PROJECT_ID         VARCHAR2(255 char),
    VALUE              CLOB
)
    /

comment on table SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE is '用户填写的表单字段值'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.FIELD_EN is '表单字段'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.FIELD_ZH is '表单字段中文名称'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.RECORD_ID is '用户的填写记录ID（比如发起流程实例ID、资源预约记录ID、任务填报记录ID）'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.HUMAN_CODE is '学号/工号'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.HUMAN_NAME is '姓名'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.ORGANIZATION_NAMES is '组织机构'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.PROJECT_ID is '项目ID（比如流程定义ID、预约资源ID、填报任务ID）'
/

comment on column SYT_NEW_PORTAL.BPM_FORM_FIELD_VALUE.VALUE is '字段对应内容'
/

create table SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION
(
    ID                 VARCHAR2(255 char) not null,
    NAME               VARCHAR2(255 char),
    REMARK             VARCHAR2(255 char),
    SORT               NUMBER(10),
    STATUS             VARCHAR2(255 char),
    URL                VARCHAR2(255 char),
    DATA_SOURCES       VARCHAR2(255 char),
    ROLEIDS            CLOB,
    ORGIDS             CLOB,
    ICON_CLASS         VARCHAR2(255 char),
    VISITOR_VISIBILITY VARCHAR2(255),
    SHOW_LOCALE        VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION is '移动端底部导航栏'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.STATUS is '状态, 启用或禁用'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.URL is '地址'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.DATA_SOURCES is '数据来源'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.ROLEIDS is '可见角色'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.ORGIDS is '可见组织机构'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.ICON_CLASS is '图标'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.VISITOR_VISIBILITY is '游客可见'
/

comment on column SYT_NEW_PORTAL.SYT_MOBILE_NAVIGATION.SHOW_LOCALE is '显示位置(1.底部菜单，2.我的列表)'
/

create table SYT_NEW_PORTAL.PLATFORM_QYWX_APP
(
    ID                 VARCHAR2(255 char) not null
        primary key,
    AGENT_ID           NUMBER(10),
    CREATED            TIMESTAMP(6),
    NAME               VARCHAR2(255 char),
    REDIRECT_URL_START VARCHAR2(255 char),
    SECRET             VARCHAR2(255 char),
    UPDATED            TIMESTAMP(6)
)
    /

create table SYT_NEW_PORTAL.PLATFORM_QYWX_PARAM
(
    KEY         VARCHAR2(20) not null
        primary key,
    DESCRIPTION VARCHAR2(255 char),
    ENABLED     NUMBER(1),
    UPDATED     TIMESTAMP(6),
    VALUE       VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.PLATFORM_QYWX_USER
(
    ID                 VARCHAR2(255 char) not null
        primary key,
    CREATED            TIMESTAMP(6),
    DESCRIPTION        VARCHAR2(255 char),
    IS_RECEIVE_MESSAGE NUMBER(1),
    SYS_ID             VARCHAR2(255 char),
    UPDATED            TIMESTAMP(6),
    WX_ID              VARCHAR2(255 char),
    UPDATED66          TIMESTAMP(6)
)
    /

create table SYT_NEW_PORTAL.SYT_STATISTICS_TAB
(
    ID      VARCHAR2(255 char) not null,
    NAME    VARCHAR2(255 char),
    REMARK  VARCHAR2(255 char),
    SORT    NUMBER(10),
    STATUS  VARCHAR2(255 char),
    URL     VARCHAR2(255 char),
    ROLEIDS CLOB,
    ORGIDS  CLOB
)
    /

comment on table SYT_NEW_PORTAL.SYT_STATISTICS_TAB is '流程统计管理'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.STATUS is '状态, 启用或禁用'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.URL is '地址'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.ROLEIDS is '可见角色'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_TAB.ORGIDS is '可见组织机构'
/

create table SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK
(
    ID            VARCHAR2(255 char) not null,
    NAME          VARCHAR2(255 char),
    REMARK        VARCHAR2(255 char),
    SORT          NUMBER(10),
    STATUS        VARCHAR2(255 char),
    MORE_URL      VARCHAR2(255 char),
    DATA_SOURCES  VARCHAR2(255 char),
    ROLEIDS       CLOB,
    ORGIDS        CLOB,
    PARENT_ID     VARCHAR2(255 char),
    SHOW_TYPE     VARCHAR2(255),
    DATA_OPTION   CLOB,
    TERMINAL_TYPE VARCHAR2(255),
    TAB_ID        VARCHAR2(255),
    NAME_SHOW     VARCHAR2(255),
    HOME_FLAG     VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK is '流程统计板块管理'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.REMARK is '备注说明'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.SORT is '排序'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.STATUS is '状态（启用、禁用）'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.MORE_URL is '更多数据地址'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.DATA_SOURCES is '数据来源'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.ROLEIDS is '可见角色'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.ORGIDS is '可见组织机构'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.PARENT_ID is '父类ID'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.SHOW_TYPE is '数据展示类型（文本、柱状图、饼图、折线图、表格等）'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.DATA_OPTION is '统计图的数据配置项'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.TERMINAL_TYPE is '终端类型（PC、MOBILE），区分PC端、移动端展示'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.TAB_ID is '流程统计管理ID，属于哪个SYT_STATIS_TAB'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.NAME_SHOW is '是否显示名称（是/否）'
/

comment on column SYT_NEW_PORTAL.SYT_STATISTICS_BLOCK.HOME_FLAG is '是否首页展示（是/否）'
/

create table SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST
(
    ID         VARCHAR2(50) not null
        primary key,
    USERNAME   VARCHAR2(255),
    USERCODE   VARCHAR2(255),
    IDCODE     VARCHAR2(50),
    STARTTIME  DATE,
    ENDTIME    DATE,
    CREATEDATE DATE,
    BRAKEID    VARCHAR2(255),
    BRAKENAME  VARCHAR2(255)
)
    /

comment on table SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST is '闸机禁用名单'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.ID is 'ID'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.USERNAME is '用户名'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.USERCODE is '账号'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.IDCODE is '身份证号'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.STARTTIME is '禁用开始时间按'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.ENDTIME is '禁用结束时间'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.CREATEDATE is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.BRAKEID is '闸机号'
/

comment on column SYT_NEW_PORTAL.SYT_BRAKE_DISABLED_LIST.BRAKENAME is '闸机名称'
/

create table SYT_NEW_PORTAL.SYT_CODE_DQZTB
(
    ID   VARCHAR2(50 char) not null,
    BZ   VARCHAR2(255 char),
    CODE VARCHAR2(255 char),
    NAME VARCHAR2(255 char)
)
    /

create table SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER
(
    ID            VARCHAR2(50) not null
        primary key,
    NAME          VARCHAR2(255),
    DESCRIPTION   VARCHAR2(4000),
    ENABLEFLAG    NUMBER,
    CREATETIME    TIMESTAMP(6),
    CREATEUSER    VARCHAR2(255),
    CHECKCRON     VARCHAR2(255),
    CONTENT       VARCHAR2(4000),
    PUSHCHANNEL   VARCHAR2(255),
    ROLES         VARCHAR2(4000),
    ORGANIZATIONS VARCHAR2(4000),
    DATASOURCE    VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.NAME is '名称'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.DESCRIPTION is '描述'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.ENABLEFLAG is '0启用 1停用'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.CREATETIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.CREATEUSER is '创建人'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.CHECKCRON is '定时表达式'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.CONTENT is '推送内容/模板'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.PUSHCHANNEL is '推送渠道'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.ROLES is '自动推送关联用户时，用于过滤角色使用'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.ORGANIZATIONS is '组织机构部门(按组织机构部门设置预警规则)'
/

comment on column SYT_NEW_PORTAL.SYT_DISPATCHING_CENTER.DATASOURCE is '数据来源'
/

create table SYT_NEW_PORTAL.SYT_SEND_RECORD
(
    ID             VARCHAR2(50) not null
        primary key,
    HUMANCODE      VARCHAR2(255),
    HUMANNAME      VARCHAR2(4000),
    CREATETIME     TIMESTAMP(6),
    DISPATCHING_ID VARCHAR2(255),
    CONTENT        VARCHAR2(4000),
    PUSHCHANNEL    VARCHAR2(255)
)
    /

comment on column SYT_NEW_PORTAL.SYT_SEND_RECORD.HUMANCODE is '账号'
/

comment on column SYT_NEW_PORTAL.SYT_SEND_RECORD.HUMANNAME is '姓名'
/

comment on column SYT_NEW_PORTAL.SYT_SEND_RECORD.CREATETIME is '创建时间'
/

comment on column SYT_NEW_PORTAL.SYT_SEND_RECORD.DISPATCHING_ID is '对应的调度任务'
/

comment on column SYT_NEW_PORTAL.SYT_SEND_RECORD.CONTENT is '推送内容'
/

comment on column SYT_NEW_PORTAL.SYT_SEND_RECORD.PUSHCHANNEL is '推送渠道'
/

